# **Product Requirements Document: MonetaSDK Native Mobile SDK (Revised with Actual Models)**

Document Version: 2.0  
Date: January 15, 2025  
Author: \[Si Huynh - Product Monetization Team\]

## **1\. Project Overview**

The MonetaSDK is a native mobile Software Development Kit (SDK) designed to provide a robust, secure, and easy-to-integrate interface for consuming the Moneta platform's core functionalities. This SDK will abstract the underlying network communication, signature generation, and data parsing, allowing third-party applications (referred to as "User SDKs") to seamlessly interact with Moneta APIs. The primary focus is on delivering a professional-grade SDK for both Android and iOS platforms, ensuring consistency, reliability, and adherence to platform-specific best practices.

**IMPORTANT**: This PRD has been updated to reflect the exact model structures from the actual Flutter implementation to ensure cross-platform consistency.

## **2\. Goals**

- **Simplify API Integration:** Provide a high-level, idiomatic API for Android and iOS developers to interact with Moneta services, reducing boilerplate code and complexity.
- **Ensure Security:** Encapsulate and enforce secure communication practices, including signature generation and header management, exactly as defined by the existing Moneta network layer.
- **Maintain Consistency:** Ensure a consistent API and behavior across Flutter, Android and iOS platforms, with exact model parity.
- **Improve Developer Experience:** Offer comprehensive and clear documentation, making it easy for developers to get started and troubleshoot issues.
- **Promote Scalability & Maintainability:** Design the SDK with internal modularity and strict access control to facilitate future enhancements and reduce technical debt.

## **3\. Target Audience**

- **Mobile Application Developers:** Android and iOS developers building applications that need to integrate with the Moneta platform.
- **Internal Development Teams:** Moneta's own mobile development teams who will maintain and extend the SDK.

## **4\. Technical Requirements**

This section details the precise technical specifications for the MonetaSDK, ensuring exact adherence to the existing Flutter project's network layer models.

### **4.1. Response/Request Class Name Models - ACTUAL IMPLEMENTATION**

All request and response data models must be defined with exact class/struct names and property names as they exist in the Flutter implementation, including their corresponding JSON keys. These models serve as the primary data structures for interaction with the Moneta APIs.

#### **Android (Kotlin/Java)**

// Generic API Response Wrapper  
import kotlinx.serialization.Serializable  
import kotlinx.serialization.json.JsonElement

@Serializable  
data class ApiResponse<T>(  
 val data: T? = null, // JSON Key: "data"  
 val success: Boolean, // JSON Key: "success"  
 val message: String? = null // JSON Key: "message"  
)

// Generic Paginated Response Wrapper  
@Serializable  
data class PaginatedResponse<T>(  
 val content: List<T>, // JSON Key: "content"  
 val pageNumber: Int, // JSON Key: "pageNumber"  
 val pageSize: Int, // JSON Key: "pageSize"  
 val totalElements: Long, // JSON Key: "totalElements"  
 val totalPages: Int // JSON Key: "totalPages"  
)

// Request Models (Based on actual Flutter implementation)  
@Serializable  
data class OnboardingRequest(  
 @SerialName("deviceName") val deviceName: String, // JSON Key: "deviceName"  
 @SerialName("deviceToken") val deviceToken: String, // JSON Key: "deviceToken"  
 @SerialName("publicKey") val publicKey: String // JSON Key: "publicKey"  
)

@Serializable  
data class ApprovalSubscriptionChargeRequest(  
 @SerialName("status") val status: String // JSON Key: "status" (enum: approved, rejected)  
)

@Serializable  
data class CreateDisputeRequest(  
 @SerialName("transactionId") val transactionId: String, // JSON Key: "transactionId"  
 @SerialName("reason") val reason: String, // JSON Key: "reason"  
 @SerialName("itemId") val itemId: String? = null // JSON Key: "itemId"  
)

@Serializable  
data class PublisherLoginRequest(  
 @SerialName("pinet_session") val session: String // JSON Key: "pinet_session"  
)

@Serializable  
data class RecommendationsRequest(  
 @SerialName("user_id") val userId: String, // JSON Key: "user_id"  
 @SerialName("user_profile") val userProfile: UserProfile, // JSON Key: "user_profile"  
 @SerialName("offset") val offset: Int, // JSON Key: "offset"  
 @SerialName("limit") val limit: Int // JSON Key: "limit"  
)

@Serializable  
data class UserProfile(  
 @SerialName("demographics") val demographics: Map<String, JsonElement> = emptyMap(), // JSON Key: "demographics"  
 @SerialName("content_preferences") val contentPreferences: ContentPreferences // JSON Key: "content_preferences"  
)

@Serializable  
data class ContentPreferences(  
 @SerialName("interests") val interests: List<String> // JSON Key: "interests"  
)

@Serializable  
data class UpdateUAUserProfileRequest(  
 @SerialName("user_id") val userId: String, // JSON Key: "user_id"  
 @SerialName("content_preferences") val contentPreferences: UAUserContentPreferences // JSON Key: "content_preferences"  
)

// Response Models (Based on actual Flutter implementation)  
@Serializable  
data class OnboardVerificationResponse(  
 @SerialName("userCode") val userCode: String, // JSON Key: "userCode"  
 @SerialName("deviceCode") val deviceCode: String, // JSON Key: "deviceCode"  
 @SerialName("tokenEndpoint") val tokenEndpoint: String, // JSON Key: "tokenEndpoint"  
 @SerialName("interval") val interval: Int // JSON Key: "interval"  
)

@Serializable  
data class OnboardUserResponse(  
 @SerialName("id") val id: String, // JSON Key: "id"  
 @SerialName("name") val name: String, // JSON Key: "name"  
 @SerialName("email") val email: String, // JSON Key: "email"  
 @SerialName("deviceId") val deviceId: String, // JSON Key: "deviceId"  
 @SerialName("membershipOrgId") val membershipOrgId: String // JSON Key: "membershipOrgId"  
)

@Serializable  
data class TransactionResponse(  
 @SerialName("id") val id: String, // JSON Key: "id"  
 @SerialName("type") val type: String, // JSON Key: "type"  
 @SerialName("userId") val userId: String, // JSON Key: "userId"  
 @SerialName("publisherId") val publisherId: String, // JSON Key: "publisherId"  
 @SerialName("publisherName") val publisherName: String, // JSON Key: "publisherName"  
 @SerialName("amount") val amount: Double, // JSON Key: "amount"  
 @SerialName("interchangeFee") val interchangeFee: Double, // JSON Key: "interchangeFee"  
 @SerialName("status") val status: String, // JSON Key: "status"  
 @SerialName("disputeStatus") val disputeStatus: String?, // JSON Key: "disputeStatus"  
 @SerialName("currencyCode") val currencyCode: String, // JSON Key: "currencyCode"  
 @SerialName("description") val description: String, // JSON Key: "description"  
 @SerialName("createdAt") val createdAt: String, // JSON Key: "createdAt" (DateTime as ISO string)  
 @SerialName("updatedAt") val updatedAt: String, // JSON Key: "updatedAt" (DateTime as ISO string)  
 @SerialName("finalizedAt") val finalizedAt: String? // JSON Key: "finalizedAt" (DateTime as ISO string)  
)

@Serializable  
data class UserFeedResponse(  
 @SerialName("id") val id: String, // JSON Key: "id"  
 @SerialName("userId") val userId: String, // JSON Key: "userId"  
 @SerialName("title") val title: String, // JSON Key: "title"  
 @SerialName("body") val body: String, // JSON Key: "body"  
 @SerialName("type") val type: String, // JSON Key: "type"  
 @SerialName("createdAt") val createdAt: String?, // JSON Key: "createdAt"  
 @SerialName("readAt") val readAt: String?, // JSON Key: "readAt"  
 @SerialName("approvalId") val approvalId: String?, // JSON Key: "approvalId"  
 @SerialName("approvalStatus") val approvalStatus: String?, // JSON Key: "approvalStatus"  
 @SerialName("approvalAt") val approvalAt: String?, // JSON Key: "approvalAt"  
 @SerialName("item") val item: Map<String, JsonElement>? // JSON Key: "item"  
)

@Serializable  
data class UAUserProfileResponse(  
 @SerialName("birth_date") val birthDate: String, // JSON Key: "birth_date"  
 @SerialName("metadata") val metadata: UAUserMetadata, // JSON Key: "metadata"  
 @SerialName("created_at") val createdAt: String, // JSON Key: "created_at"  
 @SerialName("device_ids") val deviceIds: List<String>, // JSON Key: "device_ids"  
 @SerialName("email") val email: String, // JSON Key: "email"  
 @SerialName("language") val language: String, // JSON Key: "language"  
 @SerialName("updated_at") val updatedAt: String, // JSON Key: "updated_at"  
 @SerialName("user_id") val userId: String, // JSON Key: "user_id"  
 @SerialName("content_preferences") val contentPreferences: UAUserContentPreferences, // JSON Key: "content_preferences"  
 @SerialName("quality_metrics") val qualityMetrics: UAUserQualityMetrics, // JSON Key: "quality_metrics"  
 @SerialName("communication_preferences") val communicationPreferences: UAUserCommunicationPreferences, // JSON Key: "communication_preferences"  
 @SerialName("demographics") val demographics: UAUserDemographics, // JSON Key: "demographics"  
 @SerialName("id") val id: String, // JSON Key: "id"  
 @SerialName("phone") val phone: String, // JSON Key: "phone"  
 @SerialName("moneta_user_id") val monetaUserId: String? // JSON Key: "moneta_user_id"  
)

@Serializable  
data class UAUserMetadata(  
 @SerialName("version") val version: String, // JSON Key: "version"  
 @SerialName("source_system") val sourceSystem: String // JSON Key: "source_system"  
)

@Serializable  
data class UAUserContentPreferences(  
 @SerialName("interests") val interests: List<String> // JSON Key: "interests"  
)

@Serializable  
data class UAUserQualityMetrics(  
 @SerialName("last_validated") val lastValidated: String, // JSON Key: "last_validated"  
 @SerialName("completeness_score") val completenessScore: String, // JSON Key: "completeness_score"  
 @SerialName("data_source") val dataSource: String, // JSON Key: "data_source"  
 @SerialName("confidence_score") val confidenceScore: String // JSON Key: "confidence_score"  
)

@Serializable  
data class UAUserCommunicationPreferences(  
 @SerialName("sms_opt_in") val smsOptIn: String, // JSON Key: "sms_opt_in"  
 @SerialName("email_opt_in") val emailOptIn: String, // JSON Key: "email_opt_in"  
 @SerialName("push_opt_in") val pushOptIn: String // JSON Key: "push_opt_in"  
)

@Serializable  
data class UAUserDemographics(  
 @SerialName("age") val age: String, // JSON Key: "age"  
 @SerialName("age_range") val ageRange: String, // JSON Key: "age_range"  
 @SerialName("gender") val gender: String, // JSON Key: "gender"  
 @SerialName("income_range") val incomeRange: String?, // JSON Key: "income_range"  
 @SerialName("location") val location: UAUserLocation, // JSON Key: "location"  
 @SerialName("mo_location") val moLocation: UAUserMOLocation, // JSON Key: "mo_location"  
 @SerialName("occupation") val occupation: String? // JSON Key: "occupation"  
)

@Serializable  
data class UAUserLocation(  
 @SerialName("country") val country: String // JSON Key: "country"  
)

@Serializable  
data class UAUserMOLocation(  
 @SerialName("country") val country: String, // JSON Key: "country"  
 @SerialName("city") val city: String, // JSON Key: "city"  
 @SerialName("state") val state: String // JSON Key: "state"  
)

#### **iOS (Swift)**

// Generic API Response Wrapper  
import Foundation

struct ApiResponse<T: Decodable>: Codable {  
 let data: T? // JSON Key: "data"  
 let success: Bool // JSON Key: "success"  
 let message: String? // JSON Key: "message"

    private enum CodingKeys: String, CodingKey {
        case data, success, message
    }

}

// Generic Paginated Response Wrapper  
struct PaginatedResponse<T: Decodable>: Codable {  
 let content: [T] // JSON Key: "content"  
 let pageNumber: Int // JSON Key: "pageNumber"  
 let pageSize: Int // JSON Key: "pageSize"  
 let totalElements: Int // JSON Key: "totalElements"  
 let totalPages: Int // JSON Key: "totalPages"

    private enum CodingKeys: String, CodingKey {
        case content, pageNumber, pageSize, totalElements, totalPages
    }

}

// Request Models (Based on actual Flutter implementation)  
struct OnboardingRequest: Codable {  
 let deviceName: String // JSON Key: "deviceName"  
 let deviceToken: String // JSON Key: "deviceToken"  
 let publicKey: String // JSON Key: "publicKey"

    private enum CodingKeys: String, CodingKey {
        case deviceName, deviceToken, publicKey
    }

}

struct ApprovalSubscriptionChargeRequest: Codable {  
 let status: String // JSON Key: "status" (enum: "approved", "rejected")

    private enum CodingKeys: String, CodingKey {
        case status
    }

}

struct CreateDisputeRequest: Codable {  
 let transactionId: String // JSON Key: "transactionId"  
 let reason: String // JSON Key: "reason"  
 let itemId: String? // JSON Key: "itemId"

    private enum CodingKeys: String, CodingKey {
        case transactionId, reason, itemId
    }

}

struct PublisherLoginRequest: Codable {  
 let session: String // JSON Key: "pinet_session"

    private enum CodingKeys: String, CodingKey {
        case session = "pinet_session"
    }

}

struct RecommendationsRequest: Codable {  
 let userId: String // JSON Key: "user_id"  
 let userProfile: UserProfile // JSON Key: "user_profile"  
 let offset: Int // JSON Key: "offset"  
 let limit: Int // JSON Key: "limit"

    private enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case userProfile = "user_profile"
        case offset, limit
    }

}

struct UserProfile: Codable {  
 let demographics: [String: AnyCodable] // JSON Key: "demographics"  
 let contentPreferences: ContentPreferences // JSON Key: "content_preferences"

    private enum CodingKeys: String, CodingKey {
        case demographics
        case contentPreferences = "content_preferences"
    }

}

struct ContentPreferences: Codable {  
 let interests: [String] // JSON Key: "interests"

    private enum CodingKeys: String, CodingKey {
        case interests
    }

}

struct UpdateUAUserProfileRequest: Codable {  
 let userId: String // JSON Key: "user_id"  
 let contentPreferences: UAUserContentPreferences // JSON Key: "content_preferences"

    private enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case contentPreferences = "content_preferences"
    }

}

// Response Models (Based on actual Flutter implementation)  
struct OnboardVerificationResponse: Codable {  
 let userCode: String // JSON Key: "userCode"  
 let deviceCode: String // JSON Key: "deviceCode"  
 let tokenEndpoint: String // JSON Key: "tokenEndpoint"  
 let interval: Int // JSON Key: "interval"

    private enum CodingKeys: String, CodingKey {
        case userCode, deviceCode, tokenEndpoint, interval
    }

}

struct OnboardUserResponse: Codable {  
 let id: String // JSON Key: "id"  
 let name: String // JSON Key: "name"  
 let email: String // JSON Key: "email"  
 let deviceId: String // JSON Key: "deviceId"  
 let membershipOrgId: String // JSON Key: "membershipOrgId"

    private enum CodingKeys: String, CodingKey {
        case id, name, email, deviceId, membershipOrgId
    }

}

struct TransactionResponse: Codable {  
 let id: String // JSON Key: "id"  
 let type: String // JSON Key: "type"  
 let userId: String // JSON Key: "userId"  
 let publisherId: String // JSON Key: "publisherId"  
 let publisherName: String // JSON Key: "publisherName"  
 let amount: Double // JSON Key: "amount"  
 let interchangeFee: Double // JSON Key: "interchangeFee"  
 let status: String // JSON Key: "status"  
 let disputeStatus: String? // JSON Key: "disputeStatus"  
 let currencyCode: String // JSON Key: "currencyCode"  
 let description: String // JSON Key: "description"  
 let createdAt: String // JSON Key: "createdAt" (DateTime as ISO string)  
 let updatedAt: String // JSON Key: "updatedAt" (DateTime as ISO string)  
 let finalizedAt: String? // JSON Key: "finalizedAt" (DateTime as ISO string)

    private enum CodingKeys: String, CodingKey {
        case id, type, userId, publisherId, publisherName, amount
        case interchangeFee, status, disputeStatus, currencyCode, description
        case createdAt, updatedAt, finalizedAt
    }

}

struct UserFeedResponse: Codable {  
 let id: String // JSON Key: "id"  
 let userId: String // JSON Key: "userId"  
 let title: String // JSON Key: "title"  
 let body: String // JSON Key: "body"  
 let type: String // JSON Key: "type"  
 let createdAt: String? // JSON Key: "createdAt"  
 let readAt: String? // JSON Key: "readAt"  
 let approvalId: String? // JSON Key: "approvalId"  
 let approvalStatus: String? // JSON Key: "approvalStatus"  
 let approvalAt: String? // JSON Key: "approvalAt"  
 let item: [String: AnyCodable]? // JSON Key: "item"

    private enum CodingKeys: String, CodingKey {
        case id, userId, title, body, type, createdAt, readAt
        case approvalId, approvalStatus, approvalAt, item
    }

}

struct UAUserProfileResponse: Codable {  
 let birthDate: String // JSON Key: "birth_date"  
 let metadata: UAUserMetadata // JSON Key: "metadata"  
 let createdAt: String // JSON Key: "created_at"  
 let deviceIds: [String] // JSON Key: "device_ids"  
 let email: String // JSON Key: "email"  
 let language: String // JSON Key: "language"  
 let updatedAt: String // JSON Key: "updated_at"  
 let userId: String // JSON Key: "user_id"  
 let contentPreferences: UAUserContentPreferences // JSON Key: "content_preferences"  
 let qualityMetrics: UAUserQualityMetrics // JSON Key: "quality_metrics"  
 let communicationPreferences: UAUserCommunicationPreferences // JSON Key: "communication_preferences"  
 let demographics: UAUserDemographics // JSON Key: "demographics"  
 let id: String // JSON Key: "id"  
 let phone: String // JSON Key: "phone"  
 let monetaUserId: String? // JSON Key: "moneta_user_id"

    private enum CodingKeys: String, CodingKey {
        case birthDate = "birth_date"
        case metadata
        case createdAt = "created_at"
        case deviceIds = "device_ids"
        case email, language
        case updatedAt = "updated_at"
        case userId = "user_id"
        case contentPreferences = "content_preferences"
        case qualityMetrics = "quality_metrics"
        case communicationPreferences = "communication_preferences"
        case demographics, id, phone
        case monetaUserId = "moneta_user_id"
    }

}

struct UAUserMetadata: Codable {  
 let version: String // JSON Key: "version"  
 let sourceSystem: String // JSON Key: "source_system"

    private enum CodingKeys: String, CodingKey {
        case version
        case sourceSystem = "source_system"
    }

}

struct UAUserContentPreferences: Codable {  
 let interests: [String] // JSON Key: "interests"

    private enum CodingKeys: String, CodingKey {
        case interests
    }

}

struct UAUserQualityMetrics: Codable {  
 let lastValidated: String // JSON Key: "last_validated"  
 let completenessScore: String // JSON Key: "completeness_score"  
 let dataSource: String // JSON Key: "data_source"  
 let confidenceScore: String // JSON Key: "confidence_score"

    private enum CodingKeys: String, CodingKey {
        case lastValidated = "last_validated"
        case completenessScore = "completeness_score"
        case dataSource = "data_source"
        case confidenceScore = "confidence_score"
    }

}

struct UAUserCommunicationPreferences: Codable {  
 let smsOptIn: String // JSON Key: "sms_opt_in"  
 let emailOptIn: String // JSON Key: "email_opt_in"  
 let pushOptIn: String // JSON Key: "push_opt_in"

    private enum CodingKeys: String, CodingKey {
        case smsOptIn = "sms_opt_in"
        case emailOptIn = "email_opt_in"
        case pushOptIn = "push_opt_in"
    }

}

struct UAUserDemographics: Codable {  
 let age: String // JSON Key: "age"  
 let ageRange: String // JSON Key: "age_range"  
 let gender: String // JSON Key: "gender"  
 let incomeRange: String? // JSON Key: "income_range"  
 let location: UAUserLocation // JSON Key: "location"  
 let moLocation: UAUserMOLocation // JSON Key: "mo_location"  
 let occupation: String? // JSON Key: "occupation"

    private enum CodingKeys: String, CodingKey {
        case age
        case ageRange = "age_range"
        case gender
        case incomeRange = "income_range"
        case location
        case moLocation = "mo_location"
        case occupation
    }

}

struct UAUserLocation: Codable {  
 let country: String // JSON Key: "country"

    private enum CodingKeys: String, CodingKey {
        case country
    }

}

struct UAUserMOLocation: Codable {  
 let country: String // JSON Key: "country"  
 let city: String // JSON Key: "city"  
 let state: String // JSON Key: "state"

    private enum CodingKeys: String, CodingKey {
        case country, city, state
    }

}

// Helper for AnyCodable (for dynamic JSON structures)  
struct AnyCodable: Codable {  
 let value: Any

    init(_ value: Any) {
        self.value = value
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        if let string = try? container.decode(String.self) {
            value = string
        } else if let int = try? container.decode(Int.self) {
            value = int
        } else if let double = try? container.decode(Double.self) {
            value = double
        } else if let bool = try? container.decode(Bool.self) {
            value = bool
        } else if let array = try? container.decode([AnyCodable].self) {
            value = array.map { $0.value }
        } else if let dictionary = try? container.decode([String: AnyCodable].self) {
            value = dictionary.mapValues { $0.value }
        } else {
            throw DecodingError.dataCorruptedError(in: container, debugDescription: "AnyCodable value cannot be decoded")
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        if let string = value as? String {
            try container.encode(string)
        } else if let int = value as? Int {
            try container.encode(int)
        } else if let double = value as? Double {
            try container.encode(double)
        } else if let bool = value as? Bool {
            try container.encode(bool)
        } else if let array = value as? [Any] {
            try container.encode(array.map(AnyCodable.init))
        } else if let dictionary = value as? [String: Any] {
            try container.encode(dictionary.mapValues(AnyCodable.init))
        } else {
            throw EncodingError.invalidValue(value, EncodingError.Context(codingPath: container.codingPath, debugDescription: "AnyCodable value cannot be encoded"))
        }
    }

}

### **4.2. Workflow for Creating Signatures and Headers in Interceptors**

The SDK must implement an interceptor mechanism to automatically generate and inject required headers, including the API signature. This workflow must be **kept exactly as is** from "this project's" current network implementation as described in the README.md.

**Workflow Steps (Exactly as per README.md):**

1. **Request Pre-processing:** Before sending any API request, the interceptor captures the request details.
2. **Request Annotation**: Endpoints requiring signatures are annotated with @Extra(RequiresSignature).
3. **Interceptor Detection**: MembershipOrgSignatureInterceptor checks request extras for the signature flag.
4. **Signature Generation**:
   - Retrieves user onboarding data and private key from secure storage.
   - Extracts client ID from onboarding data.
   - Generates timestamp for the request.
   - Constructs the signature using request method, URI, client ID, timestamp, and body.
   - Uses CryptoUtils.generateSignatureToken() to create RSA-SHA256 signature.
5. **Header Addition**: Adds required headers to the request:
   - Client-ID: The device's client ID
   - Signature: The generated signature with algorithm information, formatted as signature=$signature, algorithm=RSA-SHA256.
   - Request-Time: Request timestamp.
   - For UAClient specifically, x-api-key header is added.
   - Default headers (e.g., content-type) are configured.
   - Dynamic headers can be added/modified per request or globally through interceptors.
6. **Request Processing**: Proceeds with the modified request or rejects it if signature creation fails.

**Platform-Specific Implementation Notes:**

- **Android:** Utilize OkHttp's Interceptor interface for MembershipOrgSignatureInterceptor and other header additions.
- **iOS:** Implement URLSessionDelegate methods or URLRequest mutation within a custom request adapter pattern to inject headers and manage the signature workflow.

### **4.3. All Endpoints Must Be Defined Exactly Follow the API Endpoints Section in README**

The SDK will expose all endpoints specified in the "API Endpoints" section of "this project's" README.md. Each endpoint definition here must mirror that source exactly.

#### **4.3.1. MembershipOrgClient Endpoints**

| Function Name                                   | Requires Signature | Path                                                             | Parameters | Body                              | Query Parameters                                   | Response Type       |
| :---------------------------------------------- | :----------------- | :--------------------------------------------------------------- | :--------- | :-------------------------------- | :------------------------------------------------- | :------------------ |
| fetchMoInfo                                     | No                 | /api/mo/info                                                     | \-         | \-                                | \-                                                 | ApiResponse         |
| verifyOnboarding                                | No                 | /api/moneta-pass/onboard/device/{requestId}/code                 | requestId  | OnboardingRequest                 | \-                                                 | ApiResponse         |
| checkUserCodeIsVerified                         | No                 | /api/moneta-pass/onboard/device/{requestId}/token                | requestId  | \-                                | deviceCode                                         | ApiResponse         |
| fetchTransactions                               | Yes                | /api/user/transactions                                           | \-         | \-                                | month, page\[size\], page\[before\], page\[after\] | PaginatedResponse   |
| getTransaction                                  | Yes                | /api/user/transaction/{id}                                       | id         | \-                                | \-                                                 | ApiResponse         |
| getRelatedTransactions                          | Yes                | /api/user/transactions/{id}/related-transactions                 | id         | \-                                | page\[size\], page\[order\]                        | PaginatedResponse   |
| fetchUserFeeds                                  | Yes                | /api/user/feeds                                                  | \-         | \-                                | page\[size\], page\[after\]                        | PaginatedResponse   |
| getFeedById                                     | Yes                | /api/user/feeds/{feedId}                                         | feedId     | \-                                | \-                                                 | ApiResponse         |
| markFeedsAsRead                                 | Yes                | /api/user/feeds/mark-as-read                                     | \-         | Map\<String, List\>               | \-                                                 | ApiResponse         |
| deleteFeeds                                     | Yes                | /api/user/feeds                                                  | \-         | Map\<String, List\>               | \-                                                 | ApiResponse         |
| deviceRemoval                                   | Yes                | /api/user/devices/{deviceId}                                     | deviceId   | \-                                | \-                                                 | ApiResponse         |
| fetchPolicies                                   | Yes                | /api/user/policies                                               | \-         | \-                                | \-                                                 | ApiResponse\<List\> |
| setPolicy                                       | Yes                | /api/user/policies/{type}                                        | type       | Map\<String, dynamic\>            | \-                                                 | ApiResponse         |
| fetchIndustries                                 | Yes                | /api/user/policies/params/industries                             | \-         | \-                                | \-                                                 | ApiResponse\<List\> |
| fetchPublishers                                 | Yes                | /api/user/policies/params/publishers                             | \-         | \-                                | \-                                                 | ApiResponse\<List\> |
| processSubscriptionCharge                       | Yes                | /api/user/approvals/{id}                                         | id         | ApprovalSubscriptionChargeRequest | \-                                                 | ApiResponse         |
| fetchIncrements                                 | Yes                | /api/user/transactions/{id}/items                                | id         | \-                                | \-                                                 | PaginatedResponse   |
| createFetchingConsumptionActivitiesRequest      | Yes                | /api/user/transactions/{id}/consumption-activities/request       | id         | \-                                | \-                                                 | ApiResponse         |
| checkStatusFetchingConsumptionActivitiesRequest | Yes                | /api/user/transactions/{id}/consumption-activities/request/{rid} | id, rid    | \-                                | \-                                                 | ApiResponse         |
| fetchConsumptionActivities                      | Yes                | /api/user/transactions/{id}/consumption-activities               | id         | \-                                | \-                                                 | ApiResponse\<List\> |
| createDisputeRequest                            | Yes                | /api/user/disputes                                               | \-         | CreateDisputeRequest              | \-                                                 | ApiResponse         |
| fetchDisputes                                   | Yes                | /api/user/disputes                                               | \-         | \-                                | transactionId                                      | ApiResponse\<List\> |
| fetchLatestSettlementBillingCycle               | Yes                | /api/user/settlement/billing-cycles/latest                       | \-         | \-                                | \-                                                 | ApiResponse         |
| fetchSettlementBillingCycle                     | Yes                | /api/user/settlement/billing-cycles                              | \-         | \-                                | billingFrom, billingTo                             | ApiResponse\<List\> |
| fetchSettlementBillingCycleConsumption          | Yes                | /api/user/settlement/billing-cycles/{code}/consumption           | code       | \-                                | \-                                                 | ApiResponse\<List\> |
| fetchBalance                                    | Yes                | /api/user/profile/balance                                        | \-         | \-                                | \-                                                 | ApiResponse         |
| fetchDebitHistory                               | Yes                | /api/user/profile/debit-history                                  | \-         | \-                                | \-                                                 | ApiResponse         |

#### **4.3.2. MonetaCoreClient Endpoints**

| Function Name             | Requires Signature | Path                                                     | Parameters | Body                  | Query Parameters   | Response Type          |
| :------------------------ | :----------------- | :------------------------------------------------------- | :--------- | :-------------------- | :----------------- | :--------------------- |
| authPublisher             | No                 | /users/sign_in/authentication_qr_codes/{qrCodeId}/submit | qrCodeId   | PublisherLoginRequest | \-                 | PublisherLoginResponse |
| checkPublisherLoginStatus | No                 | /users/sign_in/authentication_qr_codes/{qrCodeId}        | qrCodeId   | \-                    | \-                 | PublisherLoginResponse |
| fetchArticles             | No                 | /api/v1/articles                                         | \-         | \-                    | categories, before | List                   |
| fetchArticleCategories    | No                 | /api/v1/article_categories                               | \-         | \-                    | \-                 | List                   |

#### **4.3.3. UAClient Endpoints**

| Function Name                | Requires Signature | Path                      | Parameters | Body                       | Query Parameters | Response Type             |
| :--------------------------- | :----------------- | :------------------------ | :--------- | :------------------------- | :--------------- | :------------------------ |
| fetchRssContent (deprecated) | No                 | /recommendations/{userId} | userId     | \-                         | limit, offset    | UAResponse                |
| fetchRecommendations         | No                 | /recommendations          | \-         | RecommendationsRequest     | \-               | UAResponse                |
| fetchInterests               | No                 | /interests                | \-         | \-                         | \-               | InterestsResponse         |
| fetchUserProfile             | No                 | /user-profile/{userId}    | userId     | \-                         | \-               | UAUserProfileResponse     |
| updateUserProfile            | No                 | /user-profile             | \-         | UpdateUAUserProfileRequest | \-               | UpdateUserProfileResponse |

### **4.4. All Endpoints and Related Stuffs Must Be Internal Class/Struct**

To maintain a clean public API surface and encapsulate internal implementation details, all classes, structs, and helper utilities related to network communication, data parsing, and signature generation (excluding the public MonetaSDK class and its public methods, and the public data models) must be declared as internal (Swift) or internal/package-private (Kotlin/Java).

**Examples:**

- **Android (Kotlin):**  
  internal class NetworkClient { /\* ... \*/ }  
  internal class SignatureGenerator { /\* ... \*/ }  
  internal data class InternalApiError(val code: Int, val message: String) { /\* ... \*/ }

- **iOS (Swift):**  
  internal class NetworkClient { /\* ... \*/ }  
  internal struct SignatureGenerator { /\* ... \*/ }  
  internal enum InternalAPIError: Error { /\* ... \*/ }

### **4.5. Expose All Endpoints Via Public Operation Methods**

The MonetaSDK class will expose public methods corresponding to each defined API endpoint. These methods will be the sole entry points for SDK consumers to interact with the Moneta APIs. They must be designed to be idiomatic for each platform, typically using asynchronous patterns.

**Note:** The README.md indicates different base URLs for different clients (MembershipOrgClient, MonetaCoreClient, UAClient). As per the prompt, MonetaSDK is initialized with _only a baseUrl_. The SDK will internally manage how this single baseUrl is used to construct the full URLs for the various clients (e.g., by appending specific paths like /api/mo/ or /users/ or by having internal logic to map to different domains if necessary). This design choice will be handled internally within the MonetaSDK and its InternalNetworkClients.

#### **Android (Kotlin)**

// In MonetaSDK class  
// MembershipOrgClient Endpoints  
suspend fun fetchMoInfo(): Result\<ApiResponse\<JsonElement\>\>  
suspend fun verifyOnboarding(requestId: String, request: OnboardingRequest): Result\<ApiResponse\<OnboardVerificationResponse\>\>  
suspend fun checkUserCodeIsVerified(requestId: String, deviceCode: String): Result\<ApiResponse\<OnboardUserResponse\>\>  
suspend fun fetchTransactions(month: String? \= null, pageSize: Int? \= null, pageBefore: String? \= null, pageAfter: String? \= null): Result\<PaginatedResponse\<TransactionResponse\>\>  
suspend fun getTransaction(id: String): Result\<ApiResponse\<TransactionResponse\>\>  
suspend fun getRelatedTransactions(id: String, pageSize: Int? \= null, pageOrder: String? \= null): Result\<PaginatedResponse\<TransactionResponse\>\>  
suspend fun fetchUserFeeds(pageSize: Int? \= null, pageAfter: String? \= null): Result\<PaginatedResponse\<UserFeedResponse\>\>  
suspend fun getFeedById(feedId: String): Result\<ApiResponse\<UserFeedResponse\>\>  
suspend fun markFeedsAsRead(body: Map\<String, List\<String\>\>): Result\<ApiResponse\<JsonElement\>\>  
suspend fun deleteFeeds(body: Map\<String, List\<String\>\>): Result\<ApiResponse\<JsonElement\>\>  
suspend fun deviceRemoval(deviceId: String): Result\<ApiResponse\<JsonElement\>\>  
suspend fun fetchPolicies(): Result\<ApiResponse\<List\<UserPolicyTypeResponse\>\>\>  
suspend fun setPolicy(type: String, body: Map\<String, JsonElement\>): Result\<ApiResponse\<UserPolicyDataResponse\>\>  
suspend fun fetchIndustries(): Result\<ApiResponse\<List\<IndustryResponse\>\>\>  
suspend fun fetchPublishers(): Result\<ApiResponse\<List\<IndustryResponse\>\>\>  
suspend fun processSubscriptionCharge(id: String, request: ApprovalSubscriptionChargeRequest): Result\<ApiResponse\<JsonElement\>\>  
suspend fun fetchIncrements(id: String): Result\<PaginatedResponse\<IncrementResponse\>\>  
suspend fun createFetchingConsumptionActivitiesRequest(id: String): Result\<ApiResponse\<ConsumptionActivityStatusResponse\>\>  
suspend fun checkStatusFetchingConsumptionActivitiesRequest(id: String, rid: String): Result\<ApiResponse\<ConsumptionActivityStatusResponse\>\>  
suspend fun fetchConsumptionActivities(id: String): Result\<ApiResponse\<List\<ConsumptionActivityResponse\>\>\>  
suspend fun createDisputeRequest(request: CreateDisputeRequest): Result\<ApiResponse\<DisputeRequestResponse\>\>  
suspend fun fetchDisputes(transactionId: String? \= null): Result\<ApiResponse\<List\<DisputeRequestResponse\>\>\>  
suspend fun fetchLatestSettlementBillingCycle(): Result\<ApiResponse\<UserBillingCyclesResponse\>\>  
suspend fun fetchSettlementBillingCycle(billingFrom: String? \= null, billingTo: String? \= null): Result\<ApiResponse\<List\<UserBillingCyclesResponse\>\>\>  
suspend fun fetchSettlementBillingCycleConsumption(code: String): Result\<ApiResponse\<List\<ConsumptionResponse\>\>\>  
suspend fun fetchBalance(): Result\<ApiResponse\<UserBalanceResponse\>\>  
suspend fun fetchDebitHistory(): Result\<ApiResponse\<JsonElement\>\>

// MonetaCoreClient Endpoints  
suspend fun authPublisher(qrCodeId: String, request: PublisherLoginRequest): Result\<PublisherLoginResponse\>  
suspend fun checkPublisherLoginStatus(qrCodeId: String): Result\<PublisherLoginResponse\>  
suspend fun fetchArticles(categories: String? \= null, before: String? \= null): Result\<List\<ArticleResponse\>\>  
suspend fun fetchArticleCategories(): Result\<List\<String\>\>

// UAClient Endpoints  
suspend fun fetchRssContent(userId: String, limit: Int? \= null, offset: Int? \= null): Result\<UAResponse\> // Deprecated  
suspend fun fetchRecommendations(request: RecommendationsRequest): Result\<UAResponse\>  
suspend fun fetchInterests(): Result\<InterestsResponse\>  
suspend fun fetchUserProfile(userId: String): Result\<UAUserProfileResponse\>  
suspend fun updateUserProfile(request: UpdateUAUserProfileRequest): Result\<UpdateUAUserProfileResponse\>

_Note: Result is a common pattern for handling success/failure. JsonElement is used for dynamic response types and dynamic map values._

#### **iOS (Swift)**

// In MonetaSDK class  
// MembershipOrgClient Endpoints  
func fetchMoInfo() async throws \-\> ApiResponse\<AnyCodable\>  
func verifyOnboarding(requestId: String, request: OnboardingRequest) async throws \-\> ApiResponse\<OnboardVerificationResponse\>  
func checkUserCodeIsVerified(requestId: String, deviceCode: String) async throws \-\> ApiResponse\<OnboardUserResponse\>  
func fetchTransactions(month: String? \= nil, pageSize: Int? \= nil, pageBefore: String? \= nil, pageAfter: String? \= nil) async throws \-\> PaginatedResponse\<TransactionResponse\>  
func getTransaction(id: String) async throws \-\> ApiResponse\<TransactionResponse\>  
func getRelatedTransactions(id: String, pageSize: Int? \= nil, pageOrder: String? \= nil) async throws \-\> PaginatedResponse\<TransactionResponse\>  
func fetchUserFeeds(pageSize: Int? \= nil, pageAfter: String? \= nil) async throws \-\> PaginatedResponse\<UserFeedResponse\>  
func getFeedById(feedId: String) async throws \-\> ApiResponse\<UserFeedResponse\>  
func markFeedsAsRead(body: \[String: \[String\]\]) async throws \-\> ApiResponse\<AnyCodable\>  
func deleteFeeds(body: \[String: \[String\]\]) async throws \-\> ApiResponse\<AnyCodable\>  
func deviceRemoval(deviceId: String) async throws \-\> ApiResponse\<AnyCodable\>  
func fetchPolicies() async throws \-\> ApiResponse\<\[UserPolicyTypeResponse\]\>  
func setPolicy(type: String, body: \[String: AnyCodable\]) async throws \-\> ApiResponse\<UserPolicyDataResponse\>  
func fetchIndustries() async throws \-\> ApiResponse\<\[IndustryResponse\]\>  
func fetchPublishers() async throws \-\> ApiResponse\<\[IndustryResponse\]\>  
func processSubscriptionCharge(id: String, request: ApprovalSubscriptionChargeRequest) async throws \-\> ApiResponse\<AnyCodable\>  
func fetchIncrements(id: String) async throws \-\> PaginatedResponse\<IncrementResponse\>  
func createFetchingConsumptionActivitiesRequest(id: String) async throws \-\> ApiResponse\<ConsumptionActivityStatusResponse\>  
func checkStatusFetchingConsumptionActivitiesRequest(id: String, rid: String) async throws \-\> ApiResponse\<ConsumptionActivityStatusResponse\>  
func fetchConsumptionActivities(id: String) async throws \-\> ApiResponse\<\[ConsumptionActivityResponse\]\>  
func createDisputeRequest(request: CreateDisputeRequest) async throws \-\> ApiResponse\<DisputeRequestResponse\>  
func fetchDisputes(transactionId: String? \= nil) async throws \-\> ApiResponse\<\[DisputeRequestResponse\]\>  
func fetchLatestSettlementBillingCycle() async throws \-\> ApiResponse\<UserBillingCyclesResponse\>  
func fetchSettlementBillingCycle(billingFrom: String? \= nil, billingTo: String? \= nil) async throws \-\> ApiResponse\<\[UserBillingCyclesResponse\]\>  
func fetchSettlementBillingCycleConsumption(code: String) async throws \-\> ApiResponse\<\[ConsumptionResponse\]\>  
func fetchBalance() async throws \-\> ApiResponse\<UserBalanceResponse\>  
func fetchDebitHistory() async throws \-\> ApiResponse\<AnyCodable\>

// MonetaCoreClient Endpoints  
func authPublisher(qrCodeId: String, request: PublisherLoginRequest) async throws \-\> PublisherLoginResponse  
func checkPublisherLoginStatus(qrCodeId: String) async throws \-\> PublisherLoginResponse  
func fetchArticles(categories: String? \= nil, before: String? \= nil) async throws \-\> \[ArticleResponse\]  
func fetchArticleCategories() async throws \-\> \[String\]

// UAClient Endpoints  
func fetchRssContent(userId: String, limit: Int? \= nil, offset: Int? \= nil) async throws \-\> UAResponse // Deprecated  
func fetchRecommendations(request: RecommendationsRequest) async throws \-\> UAResponse  
func fetchInterests() async throws \-\> InterestsResponse  
func fetchUserProfile(userId: String) async throws \-\> UAUserProfileResponse  
func updateUserProfile(request: UpdateUAUserProfileRequest) async throws \-\> UpdateUAUserProfileResponse

_Note: async throws is the modern Swift concurrency pattern. AnyCodable is used for dynamic or Any response types._

### **4.6. Create MonetaSDK Class Follow the Singleton Pattern**

The MonetaSDK class will be the primary interface for SDK consumers. It must strictly adhere to the Singleton pattern, ensuring only one instance exists throughout the application's lifecycle.

- **Singleton Implementation:**
  - **Android (Kotlin):** Use an object declaration or a private constructor with a companion object getInstance() method.
  - **iOS (Swift):** Use a static shared instance property.
- **Initialization:**
  - The MonetaSDK class must be initialized with **only a baseUrl parameter**. This baseUrl will be the root URL for all API calls (e.g., https://api.moneta.com). No other configuration parameters (e.g., API keys, secrets) should be required at this stage, as they are managed internally by the SDK.
  - **Clarification on baseUrl usage:** Given that the README.md indicates different base URLs for different clients (MembershipOrgClient, MonetaCoreClient, UAClient), the MonetaSDK will internally manage how the provided single baseUrl is utilized. This could involve:
    - Assuming the provided baseUrl is the primary one, and other client base URLs are derived from it (e.g., baseUrl \+ "/api/mo").
    - Internally mapping the provided baseUrl to a set of pre-configured base URLs for each client, or using it as a default if no specific base URL is configured for a sub-client.
    - **Future Consideration:** If the different clients truly require entirely separate and non-derivable base URLs, the MonetaSDK.initialize method might need to be extended in a future version to accept a configuration object containing multiple base URLs. For the initial version, the single baseUrl constraint from the prompt will be adhered to, with internal logic managing its application across clients.
  - Initialization should ideally be a one-time operation, typically performed early in the application's lifecycle.

**Example MonetaSDK Structure:**

#### **Android (Kotlin)**

import okhttp3.OkHttpClient  
import kotlinx.serialization.json.Json

object MonetaSDK {  
 private var baseUrl: String? \= null  
 private lateinit var okHttpClient: OkHttpClient  
 private lateinit var jsonSerializer: Json

    // Internal clients will be initialized based on the baseUrl and shared OkHttpClient/Json
    private lateinit var membershipOrgClient: InternalMembershipOrgClient
    private lateinit var monetaCoreClient: InternalMonetaCoreClient
    private lateinit var uaClient: InternalUAClient

    fun initialize(baseUrl: String) {
        if (this.baseUrl \!= null) {
            // Log or handle re-initialization attempt if needed
            return
        }
        this.baseUrl \= baseUrl
        this.okHttpClient \= OkHttpClient.Builder()
            // Add interceptors here for signature generation and headers
            // .addInterceptor(MembershipOrgSignatureInterceptor())
            .build()
        this.jsonSerializer \= Json { ignoreUnknownKeys \= true } // Configure as needed

        // Initialize internal clients. Example:
        // membershipOrgClient \= InternalMembershipOrgClient(okHttpClient, jsonSerializer, baseUrl \+ "/api/mo")
        // monetaCoreClient \= InternalMonetaCoreClient(okHttpClient, jsonSerializer, baseUrl \+ "/users")
        // uaClient \= InternalUAClient(okHttpClient, jsonSerializer, baseUrl \+ "/recommendations")
        // The exact mapping of baseUrl to client-specific URLs needs to be defined internally.
    }

    // Public operation methods (as defined in 4.5)
    // Example:
    // suspend fun fetchMoInfo(): Result\<ApiResponse\<JsonElement\>\> {
    //     return membershipOrgClient.fetchMoInfo()
    // }
    // ... all other public methods

}

#### **iOS (Swift)**

import Foundation  
import CryptoKit // For cryptographic operations

class MonetaSDK {  
 static let shared \= MonetaSDK() // Singleton instance

    private var baseUrl: String?
    private var urlSession: URLSession?

    // Internal clients will be initialized based on the baseUrl and shared URLSession
    private var membershipOrgClient: InternalMembershipOrgClient?
    private var monetaCoreClient: InternalMonetaCoreClient?
    private var uaClient: InternalUAClient?

    private init() { } // Private initializer to enforce singleton

    func initialize(baseUrl: String) {
        guard self.baseUrl \== nil else {
            // Log or handle re-initialization attempt if needed
            return
        }
        self.baseUrl \= baseUrl
        // Configure URLSession with a custom delegate for interceptors if needed
        let configuration \= URLSessionConfiguration.default
        self.urlSession \= URLSession(configuration: configuration, delegate: nil, delegateQueue: nil) // Delegate for interceptors can be set here

        // Initialize internal clients. Example:
        // self.membershipOrgClient \= InternalMembershipOrgClient(urlSession: urlSession\!, baseUrl: baseUrl \+ "/api/mo")
        // self.monetaCoreClient \= InternalMonetaCoreClient(urlSession: urlSession\!, baseUrl: baseUrl \+ "/users")
        // self.uaClient \= InternalUAClient(urlSession: urlSession\!, baseUrl: baseUrl \+ "/recommendations")
        // The exact mapping of baseUrl to client-specific URLs needs to be defined internally.
    }

    // Public operation methods (as defined in 4.5)
    // Example:
    // func fetchMoInfo() async throws \-\> ApiResponse\<AnyCodable\> {
    //     guard let client \= self.membershipOrgClient else { throw MonetaSDKError.notInitialized }
    //     return try await client.fetchMoInfo()
    // }
    // ... all other public methods

    enum MonetaSDKError: Error {
        case notInitialized
        case networkError(Error)
        case apiError(Int, String)
        case decodingError(Error)
    }

}

### **4.7. Tech Stack**

The implementation of the MonetaSDK will adhere to the following technology stack, prioritizing built-in platform libraries and minimizing external dependencies.

#### **4.7.1. Android SDK**

- **Language:** Kotlin
- **HTTP Client:** OkHttp (explicitly allowed external library for robust network operations)
- **JSON Serialization:** kotlinx.serialization (explicitly allowed external library for type-safe JSON parsing)
- **Concurrency:** Kotlin Coroutines (built-in, for asynchronous operations)
- **Secure Storage:** Android Keystore System (built-in, for storing sensitive data like private keys and onboarding data)
- **Cryptography:** Java Cryptography Architecture (JCA) (built-in, for RSA-SHA256 signature generation and other cryptographic needs)
- **Logging:** Android's built-in Log class.

#### **4.7.2. iOS SDK**

- **Language:** Swift
- **HTTP Client:** URLSession (built-in, for all network requests)
- **JSON Serialization:** Codable (built-in, for encoding and decoding JSON data)
- **Concurrency:** Swift Concurrency (async/await) (built-in, for asynchronous operations)
- **Secure Storage:** Keychain Services (built-in, for storing sensitive data like private keys and onboarding data)
- **Cryptography:** CryptoKit (built-in, for RSA-SHA256 signature generation and other cryptographic needs)
- **Logging:** Swift's built-in print or OSLog for structured logging.

## **5\. Super Detailed "Get Started" Documentation**

Comprehensive "Get Started" documentation will be provided for both Android and iOS, enabling developers to quickly integrate and utilize the MonetaSDK.

### **5.1. Android SDK "Get Started" Guide**

#### **5.1.1. Installation**

- **Gradle Dependency:** Instructions for adding the SDK and its required dependencies to the build.gradle file.  
  // build.gradle (app-level)  
  dependencies {  
   implementation 'com.moneta.sdk:monetasdk:1.0.0'  
   // Required for kotlinx.serialization  
   implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.3.0' // Use the latest stable version  
   // Required for OkHttp  
   implementation 'com.squareup.okhttp3:okhttp:4.9.0' // Use the latest stable version  
  }

- **Permissions:** List any required AndroidManifest.xml permissions (e.g., android.permission.INTERNET).

#### **5.1.2. Initialization**

- **Application Class:** Recommend initializing the SDK in the Application class's onCreate() method for a single, early setup.  
  import android.app.Application  
  import com.moneta.sdk.MonetaSDK

  class MyApplication : Application() {  
   override fun onCreate() {  
   super.onCreate()  
   MonetaSDK.initialize("YOUR_BASE_URL_HERE")  
   }  
  }

- **Base URL:** Emphasize that YOUR_BASE_URL_HERE should be the actual base URL of the Moneta API (e.g., https://api.moneta.com).

#### **5.1.3. Basic Usage Examples**

- **Verify Onboarding Example:** Demonstrating how to verify device onboarding.  
  import kotlinx.coroutines.launch  
  import androidx.lifecycle.lifecycleScope // For Activity/Fragment scope  
  import android.util.Log  
  import com.moneta.sdk.MonetaSDK  
  import com.moneta.sdk.model.request.OnboardingRequest  
  import com.moneta.sdk.model.response.OnboardVerificationResponse  
  import com.moneta.sdk.util.Result // Assuming a Result wrapper class

  // In an Activity or ViewModel  
  lifecycleScope.launch {  
   val requestId \= "some_unique_request_id"  
   val onboardingRequest \= OnboardingRequest(deviceCode \= "your_device_code", publicKey \= "your_public_key")  
   when (val result \= MonetaSDK.verifyOnboarding(requestId, onboardingRequest)) {  
   is Result.Success \-\> {  
   val response \= result.data  
   Log.d("MonetaSDK", "Onboarding verification successful: ${response.data?.status}")  
   }  
   is Result.Error \-\> {  
   Log.e("MonetaSDK", "Onboarding verification failed: ${result.exception.message}")  
   }  
   }  
  }

- **Fetch Transactions Example:**  
  import kotlinx.coroutines.launch  
  import androidx.lifecycle.lifecycleScope  
  import android.util.Log  
  import com.moneta.sdk.MonetaSDK  
  import com.moneta.sdk.model.response.TransactionResponse  
  import com.moneta.sdk.util.Result

  lifecycleScope.launch {  
   when (val result \= MonetaSDK.fetchTransactions(month \= "2025-05", pageSize \= 10)) {  
   is Result.Success \-\> {  
   val transactions \= result.data.content  
   transactions.forEach { Log.d("MonetaSDK", "Transaction: ${it.id}, Amount: ${it.amount}") }  
   }  
   is Result.Error \-\> {  
   Log.e("MonetaSDK", "Failed to fetch transactions: ${result.exception.message}")  
   }  
   }  
  }

- **Fetch Articles Example (MonetaCoreClient):**  
  import kotlinx.coroutines.launch  
  import androidx.lifecycle.lifecycleScope  
  import android.util.Log  
  import com.moneta.sdk.MonetaSDK  
  import com.moneta.sdk.model.response.ArticleResponse  
  import com.moneta.sdk.util.Result

  lifecycleScope.launch {  
   when (val result \= MonetaSDK.fetchArticles(categories \= "news")) {  
   is Result.Success \-\> {  
   val articles \= result.data  
   articles.forEach { Log.d("MonetaSDK", "Article: ${it.title}") }  
   }  
   is Result.Error \-\> {  
   Log.e("MonetaSDK", "Failed to fetch articles: ${result.exception.message}")  
   }  
   }  
  }

#### **5.1.4. Error Handling**

- Explain the Result wrapper or custom exception hierarchy for handling API errors (e.g., network issues, authentication failures, server-side errors).
- Provide examples of how to check error codes and display appropriate messages.

#### **5.1.5. Authentication/Authorization**

- Explain that the SDK internally manages token refresh and signature generation for MembershipOrgClient endpoints.
- Guide developers on how to handle responses that might indicate a need for re-authentication (e.g., if a token becomes invalid despite internal refresh attempts).
- Note that UAClient uses an x-api-key which is assumed to be handled internally by the SDK based on the README.md's client creation example.

#### **5.1.6. Threading/Concurrency**

- Advise calling SDK methods from background coroutines (e.g., using lifecycleScope or viewModelScope).
- Remind developers that UI updates must be performed on the main thread.

#### **5.1.7. Troubleshooting**

- Common initialization issues (e.g., incorrect baseUrl).
- Network connectivity problems.
- API error codes and their meanings.
- Debugging tips (e.g., enabling SDK logging if available).

### **5.2. iOS SDK "Get Started" Guide**

#### **5.2.1. Installation**

- **CocoaPods:** Instructions for adding to Podfile.  
  \# Podfile  
  target 'YourAppTarget' do  
   use_frameworks\!  
   pod 'MonetaSDK', '\~\> 1.0.0'  
  end

- **Swift Package Manager:** Instructions for adding via Xcode.

#### **5.2.2. Initialization**

- **AppDelegate/SceneDelegate:** Recommend initializing the SDK early in the app lifecycle.  
  // In AppDelegate.swift or SceneDelegate.swift  
  import MonetaSDK // Assuming the module name

  func application(\_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: \[UIApplication.LaunchOptionsKey: Any\]?) \-\> Bool {  
   MonetaSDK.shared.initialize(baseUrl: "YOUR_BASE_URL_HERE")  
   return true  
  }

- **Base URL:** Emphasize that YOUR_BASE_URL_HERE should be the actual base URL of the Moneta API (e.g., https://api.moneta.com).

#### **5.2.3. Basic Usage Examples**

- **Verify Onboarding Example:** Demonstrating how to verify device onboarding using async/await.  
  import Foundation // For Task  
  import MonetaSDK // Assuming the module name

  // In a ViewController or ViewModel  
  Task {  
   do {  
   let requestId \= "some_unique_request_id"  
   let onboardingRequest \= OnboardingRequest(deviceCode: "your_device_code", publicKey: "your_public_key")  
   let response \= try await MonetaSDK.shared.verifyOnboarding(requestId: requestId, request: onboardingRequest)  
   print("Onboarding verification successful: \\(response.data?.status ?? "N/A")")  
   } catch {  
   print("Onboarding verification failed: \\(error.localizedDescription)")  
   if let sdkError \= error as? MonetaSDK.MonetaSDKError {  
   print("SDK Error: \\(sdkError)")  
   }  
   }  
  }

- **Fetch Transactions Example:**  
  import Foundation  
  import MonetaSDK

  Task {  
   do {  
   let transactionsResponse \= try await MonetaSDK.shared.fetchTransactions(month: "2025-05", pageSize: 10, pageBefore: nil, pageAfter: nil)  
   transactionsResponse.content.forEach { transaction in  
   print("Transaction: \\(transaction.id), Amount: \\(transaction.amount)")  
   }  
   } catch {  
   print("Failed to fetch transactions: \\(error.localizedDescription)")  
   }  
  }

- **Fetch Articles Example (MonetaCoreClient):**  
  import Foundation  
  import MonetaSDK

  Task {  
   do {  
   let articles \= try await MonetaSDK.shared.fetchArticles(categories: "news", before: nil)  
   articles.forEach { article in  
   print("Article: \\(article.title)")  
   }  
   } catch {  
   print("Failed to fetch articles: \\(error.localizedDescription)")  
   }  
  }

#### **5.2.4. Error Handling**

- Explain the use of throws and do-catch blocks for handling errors.
- Detail the custom MonetaSDKError enum (or similar) for specific SDK-related errors (e.g., .notInitialized, .networkError, .apiError).

#### **5.2.5. Authentication/Authorization**

- Explain that the SDK internally manages token refresh and signature generation for MembershipOrgClient endpoints.
- Guide developers on how to handle responses that might indicate a need for re-authentication (e.g., if a token becomes invalid despite internal refresh attempts).
- Note that UAClient uses an x-api-key which is assumed to be handled internally by the SDK based on the README.md's client creation example.

#### **5.2.6. Threading/Concurrency**

- Explain that async/await handles threading automatically for network operations.
- Remind developers to use DispatchQueue.main.async for any UI updates if not already on the main actor.

#### **5.2.7. Troubleshooting**

- Common initialization issues (e.g., incorrect baseUrl).
- Network connectivity problems.
- API error codes and their meanings.
- Debugging tips (e.g., enabling SDK logging if available).
