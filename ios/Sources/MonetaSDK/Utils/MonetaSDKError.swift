import Foundation

public enum MonetaSDKError: Error {
    case notInitialized
    case networkError(String)
    case apiError(Int, String)
    case decodingError(String)
    case cryptoError(String)
    case secureStorageError(String)
}

extension MonetaSDKError: LocalizedError {
    public var errorDescription: String? {
        switch self {
        case .notInitialized:
            return "MonetaSDK is not initialized. Call MonetaSDK.shared.initialize() first."
        case .networkError(let message):
            return "Network error: \(message)"
        case .apiError(let code, let message):
            return "API error (\(code)): \(message)"
        case .decodingError(let message):
            return "Failed to decode response: \(message)"
        case .cryptoError(let message):
            return "Cryptography error: \(message)"
        case .secureStorageError(let message):
            return "Secure storage error: \(message)"
        }
    }
}