import Foundation

internal class InternalMembershipOrgClient {
    private let baseUrl: String
    private let networkClient: InternalNetworkClient
    private let signatureGenerator: SignatureGenerator

    init(baseUrl: String, networkClient: InternalNetworkClient, signatureGenerator: SignatureGenerator) {
        self.baseUrl = baseUrl
        self.networkClient = networkClient
        self.signatureGenerator = signatureGenerator
    }

    // MARK: - Onboarding endpoints
    func fetchMoInfo() async throws -> ApiResponse<AnyCodable> {
        guard let url = URL(string: "\(baseUrl)/info") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func verifyOnboarding(requestId: String, request: OnboardingRequest) async throws -> ApiResponse<OnboardVerificationResponse> {
        guard let url = URL(string: "\(baseUrl)/moneta-pass/onboard/device/\(requestId)/code") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.post(url: url, body: request)
    }

    func checkUserCodeIsVerified(requestId: String, deviceCode: String) async throws -> ApiResponse<OnboardUserResponse> {
        guard let url = URL(string: "\(baseUrl)/moneta-pass/onboard/device/\(requestId)/token?deviceCode=\(deviceCode)") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    // MARK: - Legacy methods for backward compatibility
    func startOnboarding(request: OnboardingRequest) async throws -> ApiResponse<OnboardVerificationResponse> {
        guard let url = URL(string: "\(baseUrl)/onboard/verify") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }

        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)

        return try await networkClient.post(url: url, body: request)
    }

    func completeOnboarding() async throws -> ApiResponse<OnboardUserResponse> {
        guard let url = URL(string: "\(baseUrl)/onboard/user") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }

        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)

        return try await networkClient.get(url: url)
    }

    func publisherLogin(session: String) async throws -> ApiResponse<OnboardUserResponse> {
        guard let url = URL(string: "\(baseUrl)/publisher/login") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }

        var urlRequest = URLRequest(url: url)
        try signatureGenerator.addSignatureHeaders(to: &urlRequest)

        let request = PublisherLoginRequest(session: session)
        return try await networkClient.post(url: url, body: request)
    }

    // MARK: - Transaction endpoints
    func fetchTransactions(
        month: String? = nil,
        pageSize: Int? = nil,
        pageBefore: String? = nil,
        pageAfter: String? = nil
    ) async throws -> ApiResponse<PaginatedResponse<TransactionResponse>> {
        var components = URLComponents(string: "\(baseUrl)/user/transactions")!
        var queryItems: [URLQueryItem] = []

        if let month = month { queryItems.append(URLQueryItem(name: "month", value: month)) }
        if let pageSize = pageSize { queryItems.append(URLQueryItem(name: "page[size]", value: String(pageSize))) }
        if let pageBefore = pageBefore { queryItems.append(URLQueryItem(name: "page[before]", value: pageBefore)) }
        if let pageAfter = pageAfter { queryItems.append(URLQueryItem(name: "page[after]", value: pageAfter)) }

        if !queryItems.isEmpty {
            components.queryItems = queryItems
        }

        guard let url = components.url else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func getTransaction(id: String) async throws -> ApiResponse<TransactionResponse> {
        guard let url = URL(string: "\(baseUrl)/user/transaction/\(id)") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func getRelatedTransactions(
        id: String,
        pageSize: Int? = nil,
        pageOrder: String? = nil
    ) async throws -> ApiResponse<PaginatedResponse<TransactionResponse>> {
        var components = URLComponents(string: "\(baseUrl)/user/transactions/\(id)/related-transactions")!
        var queryItems: [URLQueryItem] = []

        if let pageSize = pageSize { queryItems.append(URLQueryItem(name: "page[size]", value: String(pageSize))) }
        if let pageOrder = pageOrder { queryItems.append(URLQueryItem(name: "page[order]", value: pageOrder)) }

        if !queryItems.isEmpty {
            components.queryItems = queryItems
        }

        guard let url = components.url else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    // MARK: - User Feed endpoints
    func fetchUserFeeds(
        pageSize: Int? = nil,
        pageAfter: String? = nil
    ) async throws -> ApiResponse<PaginatedResponse<UserFeedResponse>> {
        var components = URLComponents(string: "\(baseUrl)/user/feeds")!
        var queryItems: [URLQueryItem] = []

        if let pageSize = pageSize { queryItems.append(URLQueryItem(name: "page[size]", value: String(pageSize))) }
        if let pageAfter = pageAfter { queryItems.append(URLQueryItem(name: "page[after]", value: pageAfter)) }

        if !queryItems.isEmpty {
            components.queryItems = queryItems
        }

        guard let url = components.url else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func getFeedById(feedId: String) async throws -> ApiResponse<UserFeedResponse> {
        guard let url = URL(string: "\(baseUrl)/user/feeds/\(feedId)") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func markFeedsAsRead(body: [String: [String]]) async throws -> ApiResponse<AnyCodable> {
        guard let url = URL(string: "\(baseUrl)/user/feeds/mark-as-read") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.post(url: url, body: body)
    }

    func deleteFeeds(body: [String: [String]]) async throws -> ApiResponse<AnyCodable> {
        guard let url = URL(string: "\(baseUrl)/user/feeds") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.delete(url: url, body: body)
    }

    // MARK: - Device management
    func deviceRemoval(deviceId: String) async throws -> ApiResponse<AnyCodable> {
        guard let url = URL(string: "\(baseUrl)/user/devices/\(deviceId)") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.delete(url: url)
    }

    // MARK: - Policy endpoints
    func fetchPolicies() async throws -> ApiResponse<[UserPolicyTypeResponse]> {
        guard let url = URL(string: "\(baseUrl)/user/policies") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func setPolicy(type: String, body: [String: AnyCodable]) async throws -> ApiResponse<UserPolicyDataResponse> {
        guard let url = URL(string: "\(baseUrl)/user/policies/\(type)") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.post(url: url, body: body)
    }

    func fetchIndustries() async throws -> ApiResponse<[IndustryResponse]> {
        guard let url = URL(string: "\(baseUrl)/user/policies/params/industries") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func fetchPublishers() async throws -> ApiResponse<[IndustryResponse]> {
        guard let url = URL(string: "\(baseUrl)/user/policies/params/publishers") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    // MARK: - Approval endpoints
    func processSubscriptionCharge(id: String, request: ApprovalSubscriptionChargeRequest) async throws -> ApiResponse<AnyCodable> {
        guard let url = URL(string: "\(baseUrl)/user/approvals/\(id)") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.post(url: url, body: request)
    }

    // MARK: - Transaction details
    func fetchIncrements(id: String) async throws -> ApiResponse<PaginatedResponse<IncrementResponse>> {
        guard let url = URL(string: "\(baseUrl)/user/transactions/\(id)/items") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    // MARK: - Consumption activities
    func createFetchingConsumptionActivitiesRequest(id: String) async throws -> ApiResponse<ConsumptionActivityStatusResponse> {
        guard let url = URL(string: "\(baseUrl)/user/transactions/\(id)/consumption-activities/request") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        let emptyBody: [String: String] = [:]
        return try await networkClient.post(url: url, body: emptyBody)
    }

    func checkStatusFetchingConsumptionActivitiesRequest(id: String, rid: String) async throws -> ApiResponse<ConsumptionActivityStatusResponse> {
        guard let url = URL(string: "\(baseUrl)/user/transactions/\(id)/consumption-activities/request/\(rid)") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func fetchConsumptionActivities(id: String) async throws -> ApiResponse<[ConsumptionActivityResponse]> {
        guard let url = URL(string: "\(baseUrl)/user/transactions/\(id)/consumption-activities") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    // MARK: - Dispute endpoints
    func createDisputeRequest(request: CreateDisputeRequest) async throws -> ApiResponse<DisputeRequestResponse> {
        guard let url = URL(string: "\(baseUrl)/user/disputes") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.post(url: url, body: request)
    }

    func fetchDisputes(transactionId: String? = nil) async throws -> ApiResponse<[DisputeRequestResponse]> {
        var components = URLComponents(string: "\(baseUrl)/user/disputes")!
        if let transactionId = transactionId {
            components.queryItems = [URLQueryItem(name: "transactionId", value: transactionId)]
        }

        guard let url = components.url else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    // MARK: - Settlement endpoints
    func fetchLatestSettlementBillingCycle() async throws -> ApiResponse<UserBillingCyclesResponse> {
        guard let url = URL(string: "\(baseUrl)/user/settlement/billing-cycles/latest") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func fetchSettlementBillingCycle(
        billingFrom: String? = nil,
        billingTo: String? = nil
    ) async throws -> ApiResponse<[UserBillingCyclesResponse]> {
        var components = URLComponents(string: "\(baseUrl)/user/settlement/billing-cycles")!
        var queryItems: [URLQueryItem] = []

        if let billingFrom = billingFrom { queryItems.append(URLQueryItem(name: "billingFrom", value: billingFrom)) }
        if let billingTo = billingTo { queryItems.append(URLQueryItem(name: "billingTo", value: billingTo)) }

        if !queryItems.isEmpty {
            components.queryItems = queryItems
        }

        guard let url = components.url else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func fetchSettlementBillingCycleConsumption(code: String) async throws -> ApiResponse<[ConsumptionResponse]> {
        guard let url = URL(string: "\(baseUrl)/user/settlement/billing-cycles/\(code)/consumption") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    // MARK: - Balance endpoints
    func fetchBalance() async throws -> ApiResponse<UserBalanceResponse> {
        guard let url = URL(string: "\(baseUrl)/user/profile/balance") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }

    func fetchDebitHistory() async throws -> ApiResponse<AnyCodable> {
        guard let url = URL(string: "\(baseUrl)/user/profile/debit-history") else {
            throw MonetaSDKError.networkError("Invalid URL")
        }
        return try await networkClient.get(url: url)
    }
}
