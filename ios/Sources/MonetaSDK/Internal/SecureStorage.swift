import Foundation
import Security

internal class SecureStorage {
    private let serviceIdentifier = "com.moneta.sdk"
    
    func saveData(_ key: String, value: String) throws {
        // Convert string to data
        guard let valueData = value.data(using: .utf8) else {
            throw MonetaSDKError.secureStorageError("Failed to convert string to data")
        }
        
        // Create query dictionary
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceIdentifier,
            kSecAttrAccount as String: key,
            kSecValueData as String: valueData,
            kSecAttrAccessible as String: kSecAttrAccessibleAfterFirstUnlockThisDeviceOnly
        ]
        
        // Delete any existing item
        SecItemDelete(query as CFDictionary)
        
        // Add the new item
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw MonetaSDKError.secureStorageError("Failed to save data to Keychain: \(status)")
        }
    }
    
    func getData(_ key: String) throws -> String? {
        // Create query dictionary
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceIdentifier,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        // Query Keychain
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        // Check query result
        if status == errSecItemNotFound {
            return nil
        }
        
        guard status == errSecSuccess else {
            throw MonetaSDKError.secureStorageError("Failed to query Keychain: \(status)")
        }
        
        // Convert result to string
        guard let data = result as? Data,
              let string = String(data: data, encoding: .utf8) else {
            throw MonetaSDKError.secureStorageError("Failed to convert data to string")
        }
        
        return string
    }
    
    func deleteData(_ key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceIdentifier,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw MonetaSDKError.secureStorageError("Failed to delete data from Keychain: \(status)")
        }
    }
}
