import Foundation
import CryptoKit

internal class SignatureGenerator {
    private let secureStorage: SecureStorage
    
    init(secureStorage: SecureStorage) {
        self.secureStorage = secureStorage
    }
    
    func addSignatureHeaders(to request: inout URLRequest) throws {
        // Check if this request requires a signature
        guard requiresSignature(request) else {
            return
        }
        
        // Get onboarding data and private key
        guard let onboardingDataString = try secureStorage.getData("onboarding_data"),
              let privateKeyString = try secureStorage.getData("private_key") else {
            return // Skip signature if no onboarding data or private key
        }
        
        // Extract client ID from onboarding data
        let clientId: String
        do {
            let onboardingData = onboardingDataString.data(using: .utf8) ?? Data()
            let onboardingResponse = try JSONDecoder().decode(OnboardUserResponse.self, from: onboardingData)
            clientId = onboardingResponse.deviceId
        } catch {
            clientId = "unknown_client_id" // Fallback if parsing fails
        }
        
        // Generate timestamp
        let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))
        
        // Get request details
        let method = request.httpMethod ?? "GET"
        let uri = request.url?.absoluteString ?? ""
        let body = request.httpBody.map { String(data: $0, encoding: .utf8) ?? "" } ?? ""
        
        // Generate signature
        let signature = try generateSignature(
            method: method,
            uri: uri,
            clientId: clientId,
            timestamp: timestamp,
            body: body,
            privateKeyString: privateKeyString
        )
        
        // Add headers
        request.addValue(clientId, forHTTPHeaderField: "Client-ID")
        request.addValue("signature=\(signature), algorithm=RSA-SHA256", forHTTPHeaderField: "Signature")
        request.addValue(timestamp, forHTTPHeaderField: "Request-Time")
    }
    
    private func requiresSignature(_ request: URLRequest) -> Bool {
        // Check for the RequiresSignature tag in request headers
        if request.value(forHTTPHeaderField: "RequiresSignature") == "true" {
            return true
        }

        // Fallback: check URL patterns that typically require signatures
        guard let url = request.url?.absoluteString else { return false }

        return url.contains("/api/mo/") ||
               url.contains("/user/") ||
               url.contains("/onboard/") ||
               url.contains("/publisher/") ||
               url.contains("/policies/") ||
               url.contains("/transactions/") ||
               url.contains("/feeds/") ||
               url.contains("/disputes/") ||
               url.contains("/settlement/") ||
               url.contains("/approvals/")
    }
    
    private func generateSignature(
        method: String,
        uri: String,
        clientId: String,
        timestamp: String,
        body: String,
        privateKeyString: String
    ) throws -> String {
        // Construct the canonical string
        let canonicalString = "\(method)\n\(uri)\n\(clientId)\n\(timestamp)\n\(body)"
        
        // Convert private key string to SecKey
        guard let privateKeyData = Data(base64Encoded: privateKeyString) else {
            throw MonetaSDKError.cryptoError("Failed to decode private key data")
        }

        let keyAttributes: [String: Any] = [
            kSecAttrKeyType as String: kSecAttrKeyTypeRSA,
            kSecAttrKeyClass as String: kSecAttrKeyClassPrivate
        ]

        var error: Unmanaged<CFError>?
        guard let privateKey = SecKeyCreateWithData(privateKeyData as CFData, keyAttributes as CFDictionary, &error) else {
            throw MonetaSDKError.cryptoError("Failed to create private key: \(error?.takeRetainedValue().localizedDescription ?? "unknown error")")
        }
        
        // Generate signature
        guard let canonicalData = canonicalString.data(using: .utf8) else {
            throw MonetaSDKError.cryptoError("Failed to convert canonical string to data")
        }
        
        var signatureError: Unmanaged<CFError>?
        guard let signatureData = SecKeyCreateSignature(
            privateKey,
            .rsaSignatureMessagePKCS1v15SHA256,
            canonicalData as CFData,
            &signatureError
        ) as Data? else {
            throw MonetaSDKError.cryptoError("Failed to generate signature: \(signatureError?.takeRetainedValue().localizedDescription ?? "unknown error")")
        }
        
        // Return Base64 encoded signature
        return signatureData.base64EncodedString()
    }
}