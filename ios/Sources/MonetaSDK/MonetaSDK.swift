import Foundation
import CryptoKit
#if canImport(UIKit)
import UIKit
#endif

public class MonetaSDK {
    public static let shared = MonetaSDK()
    
    private var baseUrl: String?
    private var urlSession: URLSession?
    private var secureStorage: SecureStorage?
    private var signatureGenerator: SignatureGenerator?
    
    // Internal clients
    private var membershipOrgClient: InternalMembershipOrgClient?
    private var monetaCoreClient: InternalMonetaCoreClient?
    private var uaClient: InternalUAClient?
    
    private init() { }
    
    public func initialize(baseUrl: String) {
        guard self.baseUrl == nil else {
            // Already initialized
            return
        }
        
        self.baseUrl = baseUrl
        
        // Initialize secure storage
        self.secureStorage = SecureStorage()
        
        // Initialize URL session with configuration
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30.0
        self.urlSession = URLSession(configuration: config)
        
        // Initialize signature generator
        guard let secureStorage = self.secureStorage else {
            return
        }
        self.signatureGenerator = SignatureGenerator(secureStorage: secureStorage)
        
        // Initialize network client
        guard let urlSession = self.urlSession,
              let signatureGenerator = self.signatureGenerator else {
            return
        }
        let networkClient = InternalNetworkClient(session: urlSession)
        
        // Initialize internal clients with appropriate base URLs
        self.membershipOrgClient = InternalMembershipOrgClient(
            baseUrl: "\(baseUrl)/api/mo",
            networkClient: networkClient,
            signatureGenerator: signatureGenerator
        )
        
        self.monetaCoreClient = InternalMonetaCoreClient(
            baseUrl: "\(baseUrl)/users",
            networkClient: networkClient,
            signatureGenerator: signatureGenerator
        )

        self.uaClient = InternalUAClient(
            baseUrl: "\(baseUrl)/recommendations",
            networkClient: networkClient,
            signatureGenerator: signatureGenerator
        )
    }
    
    private func checkInitialized() throws {
        guard baseUrl != nil,
              membershipOrgClient != nil,
              monetaCoreClient != nil,
              uaClient != nil else {
            throw MonetaSDKError.notInitialized
        }
    }
    
    // MARK: - Public API Methods
    
    // Onboarding
    public func startOnboarding() async throws -> OnboardVerificationResponse {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient,
              let secureStorage = self.secureStorage else {
            throw MonetaSDKError.notInitialized
        }
        
        // Generate RSA key pair
        let keyPair = try generateKeyPair()
        
        // Store private key securely
        let privateKeyData = try getKeyData(from: keyPair.privateKey)
        let privateKeyEncoded = privateKeyData.base64EncodedString()
        try secureStorage.saveData("private_key", value: privateKeyEncoded)
        
        // Create device token (in a real app, this would be an APNS token or similar)
        let deviceToken = generateDeviceToken()
        try secureStorage.saveData("device_token", value: deviceToken)
        
        // Create onboarding request
        let request = OnboardingRequest(
            deviceName: getDeviceName(),
            deviceToken: deviceToken,
            publicKey: try getKeyData(from: keyPair.publicKey).base64EncodedString()
        )
        
        // Make API call
        let response = try await membershipOrgClient.startOnboarding(request: request)
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    public func completeOnboarding() async throws -> OnboardUserResponse {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient,
              let secureStorage = self.secureStorage else {
            throw MonetaSDKError.notInitialized
        }
        
        let response = try await membershipOrgClient.completeOnboarding()
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        // Store onboarding data
        let encoder = JSONEncoder()
        let onboardingData = try encoder.encode(data)
        try secureStorage.saveData("onboarding_data", value: String(data: onboardingData, encoding: .utf8)!)
        
        return data
    }
    
    // Transaction methods
    public func getTransactions(page: Int, size: Int) async throws -> PaginatedResponse<TransactionResponse> {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let response = try await monetaCoreClient.getTransactions(page: page, size: size)
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    public func getTransaction(transactionId: String) async throws -> TransactionResponse {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let response = try await monetaCoreClient.getTransaction(transactionId: transactionId)
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    public func approveSubscriptionCharge(transactionId: String, status: String) async throws -> TransactionResponse {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let request = ApprovalSubscriptionChargeRequest(status: status)
        let response = try await monetaCoreClient.approveSubscriptionCharge(
            transactionId: transactionId,
            request: request
        )
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    public func createDispute(transactionId: String, reason: String, itemId: String? = nil) async throws -> Bool {
        try checkInitialized()
        guard let monetaCoreClient = self.monetaCoreClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let request = CreateDisputeRequest(
            transactionId: transactionId,
            reason: reason,
            itemId: itemId
        )
        
        let response = try await monetaCoreClient.createDispute(request: request)
        
        return response.success
    }
    
    // UA Client methods
    public func getRecommendations(userId: String, userProfile: UserProfile, offset: Int, limit: Int) async throws -> [AnyCodable] {
        try checkInitialized()
        guard let uaClient = self.uaClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let request = RecommendationsRequest(
            userId: userId,
            userProfile: userProfile,
            offset: offset,
            limit: limit
        )
        
        let response = try await uaClient.getRecommendations(request: request)
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    public func updateUserProfile(userId: String, contentPreferences: UAUserContentPreferences) async throws -> Bool {
        try checkInitialized()
        guard let uaClient = self.uaClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let request = UpdateUAUserProfileRequest(
            userId: userId,
            contentPreferences: contentPreferences
        )
        
        let response = try await uaClient.updateUserProfile(request: request)
        
        return response.success
    }
    
    // Publisher authentication
    public func authPublisher(qrCodeId: String, session: String) async throws -> OnboardUserResponse {
        try checkInitialized()
        guard let membershipOrgClient = self.membershipOrgClient else {
            throw MonetaSDKError.notInitialized
        }
        
        let response = try await membershipOrgClient.publisherLogin(session: session)
        
        guard response.success, let data = response.data else {
            throw MonetaSDKError.apiError(400, response.message ?? "Unknown error")
        }
        
        return data
    }
    
    // Helper methods
    private func generateKeyPair() throws -> (privateKey: SecKey, publicKey: SecKey) {
        let attributes: [String: Any] = [
            kSecAttrKeyType as String: kSecAttrKeyTypeRSA,
            kSecAttrKeySizeInBits as String: 2048
        ]
        
        var error: Unmanaged<CFError>?
        guard let privateKey = SecKeyCreateRandomKey(attributes as CFDictionary, &error) else {
            throw MonetaSDKError.cryptoError("Failed to generate key pair: \(error?.takeRetainedValue().localizedDescription ?? "unknown error")")
        }
        
        guard let publicKey = SecKeyCopyPublicKey(privateKey) else {
            throw MonetaSDKError.cryptoError("Failed to extract public key")
        }
        
        return (privateKey, publicKey)
    }
    
    private func generateDeviceToken() -> String {
        // In a real implementation, this would be an APNS token or similar
        // For demo purposes, generate a random string
        let bytes = [UInt8](repeating: 0, count: 32)
        let data = Data(bytes.map { _ in UInt8.random(in: 0...255) })
        return data.base64EncodedString()
    }

    private func getKeyData(from key: SecKey) throws -> Data {
        var error: Unmanaged<CFError>?
        guard let keyData = SecKeyCopyExternalRepresentation(key, &error) else {
            throw MonetaSDKError.cryptoError("Failed to extract key data: \(error?.takeRetainedValue().localizedDescription ?? "unknown error")")
        }
        return keyData as Data
    }

    private func getDeviceName() -> String {
        #if canImport(UIKit)
        return UIDevice.current.name
        #else
        return "Unknown Device"
        #endif
    }
}
