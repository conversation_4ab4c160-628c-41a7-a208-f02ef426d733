import Foundation

// Generic API Response Wrapper
public struct ApiResponse<T: Codable>: Codable {
    public let data: T?
    public let success: Bool
    public let message: String?
    
    public init(data: T?, success: Bool, message: String? = nil) {
        self.data = data
        self.success = success
        self.message = message
    }
    
    enum CodingKeys: String, CodingKey {
        case data = "data"
        case success = "success"
        case message = "message"
    }
}

// Generic Paginated Response Wrapper
public struct PaginatedResponse<T: Codable>: Codable {
    public let content: [T]
    public let pageNumber: Int
    public let pageSize: Int
    public let totalElements: Int64
    public let totalPages: Int
    
    public init(content: [T], pageNumber: Int, pageSize: Int, totalElements: Int64, totalPages: Int) {
        self.content = content
        self.pageNumber = pageNumber
        self.pageSize = pageSize
        self.totalElements = totalElements
        self.totalPages = totalPages
    }
    
    enum CodingKeys: String, CodingKey {
        case content = "content"
        case pageNumber = "pageNumber"
        case pageSize = "pageSize"
        case totalElements = "totalElements"
        case totalPages = "totalPages"
    }
}

// Response Models
public struct OnboardVerificationResponse: Codable {
    public let userCode: String
    public let deviceCode: String
    public let tokenEndpoint: String
    public let interval: Int
    
    public init(userCode: String, deviceCode: String, tokenEndpoint: String, interval: Int) {
        self.userCode = userCode
        self.deviceCode = deviceCode
        self.tokenEndpoint = tokenEndpoint
        self.interval = interval
    }
    
    enum CodingKeys: String, CodingKey {
        case userCode = "userCode"
        case deviceCode = "deviceCode"
        case tokenEndpoint = "tokenEndpoint"
        case interval = "interval"
    }
}

public struct OnboardUserResponse: Codable {
    public let id: String
    public let name: String
    public let email: String
    public let deviceId: String
    public let membershipOrgId: String
    
    public init(id: String, name: String, email: String, deviceId: String, membershipOrgId: String) {
        self.id = id
        self.name = name
        self.email = email
        self.deviceId = deviceId
        self.membershipOrgId = membershipOrgId
    }
    
    enum CodingKeys: String, CodingKey {
        case id = "id"
        case name = "name"
        case email = "email"
        case deviceId = "deviceId"
        case membershipOrgId = "membershipOrgId"
    }
}

public struct TransactionResponse: Codable {
    public let id: String
    public let type: String
    public let userId: String
    public let publisherId: String
    public let publisherName: String
    public let amount: Double
    public let interchangeFee: Double
    public let status: String
    public let disputeStatus: String?
    public let currencyCode: String
    public let description: String
    public let createdAt: String
    public let updatedAt: String
    public let finalizedAt: String?

    public init(id: String, type: String, userId: String, publisherId: String, publisherName: String, amount: Double, interchangeFee: Double, status: String, disputeStatus: String? = nil, currencyCode: String, description: String, createdAt: String, updatedAt: String, finalizedAt: String? = nil) {
        self.id = id
        self.type = type
        self.userId = userId
        self.publisherId = publisherId
        self.publisherName = publisherName
        self.amount = amount
        self.interchangeFee = interchangeFee
        self.status = status
        self.disputeStatus = disputeStatus
        self.currencyCode = currencyCode
        self.description = description
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.finalizedAt = finalizedAt
    }

    enum CodingKeys: String, CodingKey {
        case id, type, userId, publisherId, publisherName, amount
        case interchangeFee, status, disputeStatus, currencyCode, description
        case createdAt, updatedAt, finalizedAt
    }
}

public struct UserFeedResponse: Codable {
    public let id: String
    public let userId: String
    public let title: String
    public let body: String
    public let type: String
    public let createdAt: String?
    public let readAt: String?
    public let approvalId: String?
    public let approvalStatus: String?
    public let approvalAt: String?
    public let item: [String: AnyCodable]?

    public init(id: String, userId: String, title: String, body: String, type: String, createdAt: String? = nil, readAt: String? = nil, approvalId: String? = nil, approvalStatus: String? = nil, approvalAt: String? = nil, item: [String: AnyCodable]? = nil) {
        self.id = id
        self.userId = userId
        self.title = title
        self.body = body
        self.type = type
        self.createdAt = createdAt
        self.readAt = readAt
        self.approvalId = approvalId
        self.approvalStatus = approvalStatus
        self.approvalAt = approvalAt
        self.item = item
    }

    enum CodingKeys: String, CodingKey {
        case id, userId, title, body, type, createdAt, readAt
        case approvalId, approvalStatus, approvalAt, item
    }
}

public struct UAUserProfileResponse: Codable {
    public let birthDate: String
    public let metadata: UAUserMetadata
    public let createdAt: String
    public let deviceIds: [String]
    public let email: String
    public let language: String
    public let updatedAt: String
    public let userId: String
    public let contentPreferences: UAUserContentPreferences
    public let qualityMetrics: UAUserQualityMetrics
    public let communicationPreferences: UAUserCommunicationPreferences
    public let demographics: UAUserDemographics
    public let id: String
    public let phone: String
    public let monetaUserId: String?

    public init(birthDate: String, metadata: UAUserMetadata, createdAt: String, deviceIds: [String], email: String, language: String, updatedAt: String, userId: String, contentPreferences: UAUserContentPreferences, qualityMetrics: UAUserQualityMetrics, communicationPreferences: UAUserCommunicationPreferences, demographics: UAUserDemographics, id: String, phone: String, monetaUserId: String? = nil) {
        self.birthDate = birthDate
        self.metadata = metadata
        self.createdAt = createdAt
        self.deviceIds = deviceIds
        self.email = email
        self.language = language
        self.updatedAt = updatedAt
        self.userId = userId
        self.contentPreferences = contentPreferences
        self.qualityMetrics = qualityMetrics
        self.communicationPreferences = communicationPreferences
        self.demographics = demographics
        self.id = id
        self.phone = phone
        self.monetaUserId = monetaUserId
    }

    enum CodingKeys: String, CodingKey {
        case birthDate = "birth_date"
        case metadata
        case createdAt = "created_at"
        case deviceIds = "device_ids"
        case email, language
        case updatedAt = "updated_at"
        case userId = "user_id"
        case contentPreferences = "content_preferences"
        case qualityMetrics = "quality_metrics"
        case communicationPreferences = "communication_preferences"
        case demographics, id, phone
        case monetaUserId = "moneta_user_id"
    }
}

public struct UAUserMetadata: Codable {
    public let version: String
    public let sourceSystem: String

    public init(version: String, sourceSystem: String) {
        self.version = version
        self.sourceSystem = sourceSystem
    }

    enum CodingKeys: String, CodingKey {
        case version
        case sourceSystem = "source_system"
    }
}

public struct UAUserContentPreferences: Codable {
    public let interests: [String]

    public init(interests: [String]) {
        self.interests = interests
    }

    enum CodingKeys: String, CodingKey {
        case interests
    }
}

public struct UAUserQualityMetrics: Codable {
    public let lastValidated: String
    public let completenessScore: String
    public let dataSource: String
    public let confidenceScore: String

    public init(lastValidated: String, completenessScore: String, dataSource: String, confidenceScore: String) {
        self.lastValidated = lastValidated
        self.completenessScore = completenessScore
        self.dataSource = dataSource
        self.confidenceScore = confidenceScore
    }

    enum CodingKeys: String, CodingKey {
        case lastValidated = "last_validated"
        case completenessScore = "completeness_score"
        case dataSource = "data_source"
        case confidenceScore = "confidence_score"
    }
}

public struct UAUserCommunicationPreferences: Codable {
    public let smsOptIn: String
    public let emailOptIn: String
    public let pushOptIn: String

    public init(smsOptIn: String, emailOptIn: String, pushOptIn: String) {
        self.smsOptIn = smsOptIn
        self.emailOptIn = emailOptIn
        self.pushOptIn = pushOptIn
    }

    enum CodingKeys: String, CodingKey {
        case smsOptIn = "sms_opt_in"
        case emailOptIn = "email_opt_in"
        case pushOptIn = "push_opt_in"
    }
}

public struct UAUserDemographics: Codable {
    public let age: String
    public let ageRange: String
    public let gender: String
    public let incomeRange: String?
    public let location: UAUserLocation
    public let moLocation: UAUserMOLocation
    public let occupation: String?

    public init(age: String, ageRange: String, gender: String, incomeRange: String? = nil, location: UAUserLocation, moLocation: UAUserMOLocation, occupation: String? = nil) {
        self.age = age
        self.ageRange = ageRange
        self.gender = gender
        self.incomeRange = incomeRange
        self.location = location
        self.moLocation = moLocation
        self.occupation = occupation
    }

    enum CodingKeys: String, CodingKey {
        case age
        case ageRange = "age_range"
        case gender
        case incomeRange = "income_range"
        case location
        case moLocation = "mo_location"
        case occupation
    }
}

public struct UAUserLocation: Codable {
    public let country: String

    public init(country: String) {
        self.country = country
    }

    enum CodingKeys: String, CodingKey {
        case country
    }
}

public struct UAUserMOLocation: Codable {
    public let country: String
    public let city: String
    public let state: String

    public init(country: String, city: String, state: String) {
        self.country = country
        self.city = city
        self.state = state
    }

    enum CodingKeys: String, CodingKey {
        case country, city, state
    }
}

public struct PublisherLoginResponse: Codable {
    public let user: OnboardUserResponse

    public init(user: OnboardUserResponse) {
        self.user = user
    }

    enum CodingKeys: String, CodingKey {
        case user
    }
}

public struct UARecommendation: Codable {
    public let id: String
    public let title: String
    public let url: String
    public let source: String
    public let publishedAt: String

    public init(id: String, title: String, url: String, source: String, publishedAt: String) {
        self.id = id
        self.title = title
        self.url = url
        self.source = source
        self.publishedAt = publishedAt
    }

    enum CodingKeys: String, CodingKey {
        case id, title, url, source, publishedAt
    }
}

public struct UAResponse: Codable {
    public let recommendations: [UARecommendation]

    public init(recommendations: [UARecommendation]) {
        self.recommendations = recommendations
    }

    enum CodingKeys: String, CodingKey {
        case recommendations
    }
}

// Additional Response Models
public struct UserPolicyTypeResponse: Codable {
    public let type: String
    public let name: String
    public let description: String
    public let enabled: Bool

    public init(type: String, name: String, description: String, enabled: Bool) {
        self.type = type
        self.name = name
        self.description = description
        self.enabled = enabled
    }

    enum CodingKeys: String, CodingKey {
        case type, name, description, enabled
    }
}

public struct UserPolicyDataResponse: Codable {
    public let type: String
    public let data: [String: AnyCodable]

    public init(type: String, data: [String: AnyCodable]) {
        self.type = type
        self.data = data
    }

    enum CodingKeys: String, CodingKey {
        case type, data
    }
}

public struct IndustryResponse: Codable {
    public let id: String
    public let name: String
    public let code: String

    public init(id: String, name: String, code: String) {
        self.id = id
        self.name = name
        self.code = code
    }

    enum CodingKeys: String, CodingKey {
        case id, name, code
    }
}

public struct IncrementResponse: Codable {
    public let id: String
    public let transactionId: String
    public let amount: Double
    public let description: String
    public let timestamp: String

    public init(id: String, transactionId: String, amount: Double, description: String, timestamp: String) {
        self.id = id
        self.transactionId = transactionId
        self.amount = amount
        self.description = description
        self.timestamp = timestamp
    }

    enum CodingKeys: String, CodingKey {
        case id, transactionId, amount, description, timestamp
    }
}

public struct ConsumptionActivityStatusResponse: Codable {
    public let requestId: String
    public let status: String
    public let progress: Int
    public let message: String?

    public init(requestId: String, status: String, progress: Int, message: String? = nil) {
        self.requestId = requestId
        self.status = status
        self.progress = progress
        self.message = message
    }

    enum CodingKeys: String, CodingKey {
        case requestId, status, progress, message
    }
}

public struct ConsumptionActivityResponse: Codable {
    public let id: String
    public let transactionId: String
    public let activity: String
    public let timestamp: String
    public let metadata: [String: AnyCodable]?

    public init(id: String, transactionId: String, activity: String, timestamp: String, metadata: [String: AnyCodable]? = nil) {
        self.id = id
        self.transactionId = transactionId
        self.activity = activity
        self.timestamp = timestamp
        self.metadata = metadata
    }

    enum CodingKeys: String, CodingKey {
        case id, transactionId, activity, timestamp, metadata
    }
}

public struct DisputeRequestResponse: Codable {
    public let id: String
    public let transactionId: String
    public let reason: String
    public let status: String
    public let createdAt: String
    public let updatedAt: String
    public let itemId: String?

    public init(id: String, transactionId: String, reason: String, status: String, createdAt: String, updatedAt: String, itemId: String? = nil) {
        self.id = id
        self.transactionId = transactionId
        self.reason = reason
        self.status = status
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.itemId = itemId
    }

    enum CodingKeys: String, CodingKey {
        case id, transactionId, reason, status, createdAt, updatedAt, itemId
    }
}

public struct UserBillingCyclesResponse: Codable {
    public let code: String
    public let billingFrom: String
    public let billingTo: String
    public let status: String
    public let totalAmount: Double
    public let currency: String

    public init(code: String, billingFrom: String, billingTo: String, status: String, totalAmount: Double, currency: String) {
        self.code = code
        self.billingFrom = billingFrom
        self.billingTo = billingTo
        self.status = status
        self.totalAmount = totalAmount
        self.currency = currency
    }

    enum CodingKeys: String, CodingKey {
        case code, billingFrom, billingTo, status, totalAmount, currency
    }
}

public struct ConsumptionResponse: Codable {
    public let id: String
    public let billingCycleCode: String
    public let publisherId: String
    public let publisherName: String
    public let amount: Double
    public let currency: String
    public let consumptionDate: String

    public init(id: String, billingCycleCode: String, publisherId: String, publisherName: String, amount: Double, currency: String, consumptionDate: String) {
        self.id = id
        self.billingCycleCode = billingCycleCode
        self.publisherId = publisherId
        self.publisherName = publisherName
        self.amount = amount
        self.currency = currency
        self.consumptionDate = consumptionDate
    }

    enum CodingKeys: String, CodingKey {
        case id, billingCycleCode, publisherId, publisherName, amount, currency, consumptionDate
    }
}

public struct UserBalanceResponse: Codable {
    public let balance: Double
    public let currency: String
    public let lastUpdated: String
    public let pendingAmount: Double?

    public init(balance: Double, currency: String, lastUpdated: String, pendingAmount: Double? = nil) {
        self.balance = balance
        self.currency = currency
        self.lastUpdated = lastUpdated
        self.pendingAmount = pendingAmount
    }

    enum CodingKeys: String, CodingKey {
        case balance, currency, lastUpdated, pendingAmount
    }
}

public struct ArticleResponse: Codable {
    public let id: String
    public let title: String
    public let content: String
    public let category: String
    public let publishedAt: String
    public let author: String?
    public let url: String?

    public init(id: String, title: String, content: String, category: String, publishedAt: String, author: String? = nil, url: String? = nil) {
        self.id = id
        self.title = title
        self.content = content
        self.category = category
        self.publishedAt = publishedAt
        self.author = author
        self.url = url
    }

    enum CodingKeys: String, CodingKey {
        case id, title, content, category, publishedAt, author, url
    }
}

public struct InterestsResponse: Codable {
    public let interests: [String]
    public let categories: [String]

    public init(interests: [String], categories: [String]) {
        self.interests = interests
        self.categories = categories
    }

    enum CodingKeys: String, CodingKey {
        case interests, categories
    }
}

public struct UpdateUAUserProfileResponse: Codable {
    public let success: Bool
    public let message: String?
    public let updatedAt: String?

    public init(success: Bool, message: String? = nil, updatedAt: String? = nil) {
        self.success = success
        self.message = message
        self.updatedAt = updatedAt
    }

    enum CodingKeys: String, CodingKey {
        case success, message, updatedAt
    }
}
