---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftmodule'
dependencies:
  - mtime:           1746401355000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftmodule'
    size:            158404
  - mtime:           1745034242000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1929969
    sdk_relative:    true
  - mtime:           1745035158000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1745043435000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1745030861000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1745035393000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            3890
    sdk_relative:    true
  - mtime:           1745035397000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1065
    sdk_relative:    true
  - mtime:           1745035418000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1101
    sdk_relative:    true
  - mtime:           1745035418000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1102
    sdk_relative:    true
  - mtime:           1745035414000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            1513
    sdk_relative:    true
  - mtime:           1745035426000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            853
    sdk_relative:    true
  - mtime:           1745035393000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            15284
    sdk_relative:    true
  - mtime:           1745034435000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            4262
    sdk_relative:    true
  - mtime:           1745035443000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            18255
    sdk_relative:    true
  - mtime:           1745035849000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            230631
    sdk_relative:    true
  - mtime:           1745035971000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22908
    sdk_relative:    true
  - mtime:           1745036547000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            167834
    sdk_relative:    true
  - mtime:           1745036488000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            6597
    sdk_relative:    true
  - mtime:           1745036728000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            57170
    sdk_relative:    true
  - mtime:           1745037002000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface'
    size:            22859
    sdk_relative:    true
version:         1
...
