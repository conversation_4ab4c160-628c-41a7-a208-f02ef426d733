import Foundation

internal class InternalNetworkClient {
    private let session: URLSession
    private let jsonDecoder: JSONDecoder
    private let jsonEncoder: JSONEncoder
    
    init(session: URLSession = .shared) {
        self.session = session
        self.jsonDecoder = JSONDecoder()
        self.jsonEncoder = JSONEncoder()
    }
    
    func get<T: Decodable>(url: URL, headers: [String: String] = [:]) async throws -> ApiResponse<T> {
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // Add headers
        headers.forEach { request.addValue($1, forHTTPHeaderField: $0) }
        
        return try await executeRequest(request)
    }
    
    func post<T: Encodable, R: Decodable>(url: URL, body: T, headers: [String: String] = [:]) async throws -> ApiResponse<R> {
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.httpBody = try jsonEncoder.encode(body)
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add headers
        headers.forEach { request.addValue($1, forHTTPHeaderField: $0) }
        
        return try await executeRequest(request)
    }
    
    func put<T: Encodable, R: Decodable>(url: URL, body: T, headers: [String: String] = [:]) async throws -> ApiResponse<R> {
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.httpBody = try jsonEncoder.encode(body)
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add headers
        headers.forEach { request.addValue($1, forHTTPHeaderField: $0) }
        
        return try await executeRequest(request)
    }
    
    func delete<T: Decodable>(url: URL, headers: [String: String] = [:]) async throws -> ApiResponse<T> {
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        
        // Add headers
        headers.forEach { request.addValue($1, forHTTPHeaderField: $0) }
        
        return try await executeRequest(request)
    }
    
    private func executeRequest<T: Decodable>(_ request: URLRequest) async throws -> ApiResponse<T> {
        do {
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw MonetaSDKError.networkError("Invalid response type")
            }
            
            if !(200...299).contains(httpResponse.statusCode) {
                throw MonetaSDKError.apiError(httpResponse.statusCode, "API error")
            }
            
            do {
                return try jsonDecoder.decode(ApiResponse<T>.self, from: data)
            } catch {
                throw MonetaSDKError.decodingError("Failed to decode response: \(error.localizedDescription)")
            }
        } catch let error as MonetaSDKError {
            throw error
        } catch {
            throw MonetaSDKError.networkError("Network request failed: \(error.localizedDescription)")
        }
    }
}