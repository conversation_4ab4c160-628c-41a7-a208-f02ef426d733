import Foundation

public struct ApiResponse<T: Codable>: Codable {
    public let data: T?
    public let success: Bool
    public let message: String?
    
    enum CodingKeys: String, CodingKey {
        case data
        case success
        case message
    }
}

public struct PaginatedResponse<T: Codable>: Codable {
    public let content: [T]
    public let pageNumber: Int
    public let pageSize: Int
    public let totalElements: Int64
    public let totalPages: Int
    
    enum CodingKeys: String, CodingKey {
        case content
        case pageNumber
        case pageSize
        case totalElements
        case totalPages
    }
}