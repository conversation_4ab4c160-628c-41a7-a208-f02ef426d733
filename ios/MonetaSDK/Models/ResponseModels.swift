import Foundation

// Generic API Response Wrapper
public struct ApiResponse<T: Codable>: Codable {
    public let data: T?
    public let success: Bool
    public let message: String?
    
    public init(data: T?, success: Bool, message: String? = nil) {
        self.data = data
        self.success = success
        self.message = message
    }
    
    enum CodingKeys: String, CodingKey {
        case data = "data"
        case success = "success"
        case message = "message"
    }
}

// Generic Paginated Response Wrapper
public struct PaginatedResponse<T: Codable>: Codable {
    public let content: [T]
    public let pageNumber: Int
    public let pageSize: Int
    public let totalElements: Int64
    public let totalPages: Int
    
    public init(content: [T], pageNumber: Int, pageSize: Int, totalElements: Int64, totalPages: Int) {
        self.content = content
        self.pageNumber = pageNumber
        self.pageSize = pageSize
        self.totalElements = totalElements
        self.totalPages = totalPages
    }
    
    enum CodingKeys: String, CodingKey {
        case content = "content"
        case pageNumber = "pageNumber"
        case pageSize = "pageSize"
        case totalElements = "totalElements"
        case totalPages = "totalPages"
    }
}

// Response Models
public struct OnboardVerificationResponse: Codable {
    public let userCode: String
    public let deviceCode: String
    public let tokenEndpoint: String
    public let interval: Int
    
    public init(userCode: String, deviceCode: String, tokenEndpoint: String, interval: Int) {
        self.userCode = userCode
        self.deviceCode = deviceCode
        self.tokenEndpoint = tokenEndpoint
        self.interval = interval
    }
    
    enum CodingKeys: String, CodingKey {
        case userCode = "userCode"
        case deviceCode = "deviceCode"
        case tokenEndpoint = "tokenEndpoint"
        case interval = "interval"
    }
}

public struct OnboardUserResponse: Codable {
    public let id: String
    public let name: String
    public let email: String
    public let deviceId: String
    public let membershipOrgId: String
    
    public init(id: String, name: String, email: String, deviceId: String, membershipOrgId: String) {
        self.id = id
        self.name = name
        self.email = email
        self.deviceId = deviceId
        self.membershipOrgId = membershipOrgId
    }
    
    enum CodingKeys: String, CodingKey {
        case id = "id"
        case name = "name"
        case email = "email"
        case deviceId = "deviceId"
        case membershipOrgId = "membershipOrgId"
    }
}

public struct TransactionResponse: Codable {
    public let id: String
    public let type: String
    public let userId: String
    public let publisherId: String
    // Additional fields as needed
    public let amount: Double?
    public let currency: String?
    public let status: String?
    public let createdAt: String?
    
    public init(id: String, type: String, userId: String, publisherId: String, amount: Double? = nil, currency: String? = nil, status: String? = nil, createdAt: String? = nil) {
        self.id = id
        self.type = type
        self.userId = userId
        self.publisherId = publisherId
        self.amount = amount
        self.currency = currency
        self.status = status
        self.createdAt = createdAt
    }
    
    enum CodingKeys: String, CodingKey {
        case id = "id"
        case type = "type"
        case userId = "userId"
        case publisherId = "publisherId"
        case amount = "amount"
        case currency = "currency"
        case status = "status"
        case createdAt = "createdAt"
    }
}
