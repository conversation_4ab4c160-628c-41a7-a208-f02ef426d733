import XCTest
@testable import MonetaSDK

final class IntegrationTests: XCTestCase {
    
    var sdk: MonetaSDK!
    let testBaseUrl = "https://api-test.moneta.com"
    
    override func setUp() {
        super.setUp()
        sdk = MonetaSDK.shared
        sdk.initialize(baseUrl: testBaseUrl)
    }
    
    override func tearDown() {
        sdk = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testSDKInitialization() {
        XCTAssertNotNil(sdk)
        // Test that SDK is properly initialized
        // In a real test, you might check internal state or make a simple API call
    }
    
    // MARK: - Onboarding Flow Tests
    
    func testOnboardingFlow() async throws {
        // This is a mock test - in a real implementation, you would use a test server
        // or mock the network responses
        
        // Test 1: Start onboarding
        do {
            let verificationResponse = try await sdk.startOnboarding()
            XCTAssertNotNil(verificationResponse.userCode)
            XCTAssertNotNil(verificationResponse.deviceCode)
            XCTAssertNotNil(verificationResponse.tokenEndpoint)
            XCTAssertGreaterThan(verificationResponse.interval, 0)
        } catch {
            // Expected to fail in test environment without proper server
            XCTAssertTrue(error is MonetaSDKError)
        }
        
        // Test 2: Complete onboarding
        do {
            let userResponse = try await sdk.completeOnboarding()
            XCTAssertNotNil(userResponse.id)
            XCTAssertNotNil(userResponse.deviceId)
        } catch {
            // Expected to fail in test environment without proper server
            XCTAssertTrue(error is MonetaSDKError)
        }
    }
    
    // MARK: - Transaction Tests
    
    func testGetTransactions() async throws {
        do {
            let transactions = try await sdk.getTransactions(page: 1, size: 10)
            XCTAssertNotNil(transactions)
            // In a real test with mock data, you would verify the structure
        } catch {
            // Expected to fail in test environment without proper server
            XCTAssertTrue(error is MonetaSDKError)
        }
    }
    
    func testGetSingleTransaction() async throws {
        let testTransactionId = "test_txn_123"
        
        do {
            let transaction = try await sdk.getTransaction(transactionId: testTransactionId)
            XCTAssertEqual(transaction.id, testTransactionId)
        } catch {
            // Expected to fail in test environment without proper server
            XCTAssertTrue(error is MonetaSDKError)
        }
    }
    
    func testApproveSubscriptionCharge() async throws {
        let testTransactionId = "test_txn_123"
        let status = "approved"

        do {
            let result = try await sdk.approveSubscriptionCharge(
                transactionId: testTransactionId,
                status: status
            )
            XCTAssertNotNil(result)
        } catch {
            // Expected to fail in test environment without proper server
            XCTAssertTrue(error is MonetaSDKError)
        }
    }
    
    func testCreateDispute() async throws {
        do {
            let success = try await sdk.createDispute(
                transactionId: "test_txn_123",
                reason: "Unauthorized charge",
                itemId: "test_item_456"
            )
            XCTAssertTrue(success)
        } catch {
            // Expected to fail in test environment without proper server
            XCTAssertTrue(error is MonetaSDKError)
        }
    }
    
    // MARK: - User Profile Tests
    
    func testGetRecommendations() async throws {
        let contentPreferences = ContentPreferences(interests: ["tech", "science"])
        let userProfile = UserProfile(contentPreferences: contentPreferences)

        do {
            let recommendations = try await sdk.getRecommendations(
                userId: "test_user_123",
                userProfile: userProfile,
                offset: 0,
                limit: 5
            )
            XCTAssertNotNil(recommendations)
        } catch {
            // Expected to fail in test environment without proper server
            XCTAssertTrue(error is MonetaSDKError)
        }
    }
    
    func testUpdateUserProfile() async throws {
        let contentPreferences = UAUserContentPreferences(interests: ["tech", "science"])
        
        do {
            let success = try await sdk.updateUserProfile(
                userId: "test_user_123",
                contentPreferences: contentPreferences
            )
            XCTAssertTrue(success)
        } catch {
            // Expected to fail in test environment without proper server
            XCTAssertTrue(error is MonetaSDKError)
        }
    }
    
    // MARK: - Publisher Authentication Tests
    
    func testPublisherLogin() async throws {
        let testSession = "test_session_token"

        do {
            let user = try await sdk.authPublisher(qrCodeId: "test_qr_123", session: testSession)
            XCTAssertNotNil(user.id)
            XCTAssertNotNil(user.deviceId)
        } catch {
            // Expected to fail in test environment without proper server
            XCTAssertTrue(error is MonetaSDKError)
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testNetworkErrorHandling() async {
        // Test with invalid base URL - reinitialize with invalid URL
        sdk.initialize(baseUrl: "invalid-url")

        do {
            _ = try await sdk.startOnboarding()
            XCTFail("Should have thrown an error")
        } catch {
            XCTAssertTrue(error is MonetaSDKError)
            if case .networkError(let message) = error as? MonetaSDKError {
                XCTAssertFalse(message.isEmpty)
            }
        }

        // Restore valid URL for other tests
        sdk.initialize(baseUrl: testBaseUrl)
    }
    
    // MARK: - Signature Tests
    
    func testSignatureGeneration() throws {
        // Test signature generation with mock data
        let secureStorage = SecureStorage()
        let signatureGenerator = SignatureGenerator(secureStorage: secureStorage)
        
        // Store mock onboarding data and private key for testing
        let mockOnboardingData = """
        {
            "id": "user_123",
            "name": "Test User",
            "email": "<EMAIL>",
            "deviceId": "device_456",
            "membershipOrgId": "org_789"
        }
        """
        
        let mockPrivateKey = "mock_private_key_base64"
        
        do {
            try secureStorage.saveData("onboarding_data", value: mockOnboardingData)
            try secureStorage.saveData("private_key", value: mockPrivateKey)
            
            var request = URLRequest(url: URL(string: "https://api.example.com/user/test")!)
            request.httpMethod = "POST"
            request.httpBody = "test body".data(using: .utf8)
            
            // This should not throw an error even if signature generation fails
            // because the signature generator should handle errors gracefully
            try signatureGenerator.addSignatureHeaders(to: &request)
            
            // Verify that headers were added (or not, depending on implementation)
            // In a real test, you would verify the actual signature
            
        } catch {
            // Signature generation might fail with mock data, which is expected
            XCTAssertTrue(error is MonetaSDKError)
        }
    }
    
    // MARK: - Performance Tests
    
    func testPerformanceOfModelSerialization() {
        let transaction = TransactionResponse(
            id: "txn_123",
            type: "subscription",
            userId: "user_456",
            publisherId: "pub_789",
            publisherName: "Test Publisher",
            amount: 9.99,
            interchangeFee: 0.30,
            status: "completed",
            disputeStatus: nil,
            currencyCode: "USD",
            description: "Monthly subscription",
            createdAt: "2023-01-01T00:00:00Z",
            updatedAt: "2023-01-01T00:05:00Z",
            finalizedAt: nil
        )
        
        measure {
            for _ in 0..<1000 {
                do {
                    let encoder = JSONEncoder()
                    let data = try encoder.encode(transaction)
                    let decoder = JSONDecoder()
                    _ = try decoder.decode(TransactionResponse.self, from: data)
                } catch {
                    XCTFail("Serialization failed: \(error)")
                }
            }
        }
    }
}
