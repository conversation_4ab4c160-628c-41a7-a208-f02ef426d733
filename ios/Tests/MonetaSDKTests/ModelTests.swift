import XCTest
@testable import MonetaSDK

final class ModelTests: XCTestCase {
    
    // MARK: - Request Models Tests
    
    func testOnboardingRequestSerialization() throws {
        let request = OnboardingRequest(
            deviceName: "Test Device",
            deviceToken: "test_token_123",
            publicKey: "test_public_key"
        )
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(request)
        
        let decoder = JSONDecoder()
        let decodedRequest = try decoder.decode(OnboardingRequest.self, from: data)
        
        XCTAssertEqual(request.deviceName, decodedRequest.deviceName)
        XCTAssertEqual(request.deviceToken, decodedRequest.deviceToken)
        XCTAssertEqual(request.publicKey, decodedRequest.publicKey)
    }
    
    func testApprovalSubscriptionChargeRequestSerialization() throws {
        let request = ApprovalSubscriptionChargeRequest(status: "approved")

        let encoder = JSONEncoder()
        let data = try encoder.encode(request)

        let decoder = JSONDecoder()
        let decodedRequest = try decoder.decode(ApprovalSubscriptionChargeRequest.self, from: data)

        XCTAssertEqual(request.status, decodedRequest.status)
    }
    
    func testCreateDisputeRequestSerialization() throws {
        let request = CreateDisputeRequest(
            transactionId: "txn_123",
            reason: "Unauthorized charge",
            itemId: "item_456"
        )
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(request)
        
        let decoder = JSONDecoder()
        let decodedRequest = try decoder.decode(CreateDisputeRequest.self, from: data)
        
        XCTAssertEqual(request.transactionId, decodedRequest.transactionId)
        XCTAssertEqual(request.reason, decodedRequest.reason)
        XCTAssertEqual(request.itemId, decodedRequest.itemId)
    }
    
    func testPublisherLoginRequestSerialization() throws {
        let request = PublisherLoginRequest(session: "session_token_123")
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(request)
        
        let decoder = JSONDecoder()
        let decodedRequest = try decoder.decode(PublisherLoginRequest.self, from: data)
        
        XCTAssertEqual(request.session, decodedRequest.session)
    }
    
    func testRecommendationsRequestSerialization() throws {
        let contentPreferences = ContentPreferences(interests: ["tech", "science"])
        let userProfile = UserProfile(contentPreferences: contentPreferences)
        let request = RecommendationsRequest(
            userId: "user_123",
            userProfile: userProfile,
            offset: 0,
            limit: 10
        )

        let encoder = JSONEncoder()
        let data = try encoder.encode(request)

        let decoder = JSONDecoder()
        let decodedRequest = try decoder.decode(RecommendationsRequest.self, from: data)

        XCTAssertEqual(request.userId, decodedRequest.userId)
        XCTAssertEqual(request.limit, decodedRequest.limit)
        XCTAssertEqual(request.offset, decodedRequest.offset)
        XCTAssertEqual(request.userProfile.contentPreferences.interests, decodedRequest.userProfile.contentPreferences.interests)
    }
    
    func testUpdateUAUserProfileRequestSerialization() throws {
        let contentPreferences = UAUserContentPreferences(interests: ["tech", "science"])
        let request = UpdateUAUserProfileRequest(
            userId: "user_123",
            contentPreferences: contentPreferences
        )
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(request)
        
        let decoder = JSONDecoder()
        let decodedRequest = try decoder.decode(UpdateUAUserProfileRequest.self, from: data)
        
        XCTAssertEqual(request.userId, decodedRequest.userId)
        XCTAssertEqual(request.contentPreferences.interests, decodedRequest.contentPreferences.interests)
    }
    
    // MARK: - Response Models Tests
    
    func testOnboardVerificationResponseDeserialization() throws {
        let json = """
        {
            "userCode": "USER123",
            "deviceCode": "DEVICE456",
            "tokenEndpoint": "https://api.example.com/token",
            "interval": 5
        }
        """
        
        let data = json.data(using: .utf8)!
        let decoder = JSONDecoder()
        let response = try decoder.decode(OnboardVerificationResponse.self, from: data)
        
        XCTAssertEqual(response.userCode, "USER123")
        XCTAssertEqual(response.deviceCode, "DEVICE456")
        XCTAssertEqual(response.tokenEndpoint, "https://api.example.com/token")
        XCTAssertEqual(response.interval, 5)
    }
    
    func testOnboardUserResponseDeserialization() throws {
        let json = """
        {
            "id": "user_123",
            "name": "Test User",
            "email": "<EMAIL>",
            "deviceId": "device_456",
            "membershipOrgId": "org_789"
        }
        """
        
        let data = json.data(using: .utf8)!
        let decoder = JSONDecoder()
        let response = try decoder.decode(OnboardUserResponse.self, from: data)
        
        XCTAssertEqual(response.id, "user_123")
        XCTAssertEqual(response.name, "Test User")
        XCTAssertEqual(response.email, "<EMAIL>")
        XCTAssertEqual(response.deviceId, "device_456")
        XCTAssertEqual(response.membershipOrgId, "org_789")
    }
    
    func testTransactionResponseDeserialization() throws {
        let json = """
        {
            "id": "txn_123",
            "type": "subscription",
            "userId": "user_456",
            "publisherId": "pub_789",
            "publisherName": "Test Publisher",
            "amount": 9.99,
            "interchangeFee": 0.30,
            "status": "completed",
            "currencyCode": "USD",
            "description": "Monthly subscription",
            "createdAt": "2023-01-01T00:00:00Z",
            "updatedAt": "2023-01-01T00:05:00Z"
        }
        """
        
        let data = json.data(using: .utf8)!
        let decoder = JSONDecoder()
        let response = try decoder.decode(TransactionResponse.self, from: data)
        
        XCTAssertEqual(response.id, "txn_123")
        XCTAssertEqual(response.type, "subscription")
        XCTAssertEqual(response.userId, "user_456")
        XCTAssertEqual(response.publisherId, "pub_789")
        XCTAssertEqual(response.publisherName, "Test Publisher")
        XCTAssertEqual(response.amount, 9.99)
        XCTAssertEqual(response.interchangeFee, 0.30)
        XCTAssertEqual(response.status, "completed")
        XCTAssertEqual(response.currencyCode, "USD")
        XCTAssertEqual(response.description, "Monthly subscription")
        XCTAssertEqual(response.createdAt, "2023-01-01T00:00:00Z")
        XCTAssertEqual(response.updatedAt, "2023-01-01T00:05:00Z")
    }
    
    func testUAUserProfileResponseDeserialization() throws {
        let json = """
        {
            "birth_date": "1990-01-01",
            "metadata": {
                "version": "1.0",
                "source_system": "test"
            },
            "created_at": "2023-01-01T00:00:00Z",
            "device_ids": ["device1", "device2"],
            "email": "<EMAIL>",
            "language": "en",
            "updated_at": "2023-01-01T00:05:00Z",
            "user_id": "user_123",
            "content_preferences": {
                "interests": ["tech", "science"]
            },
            "quality_metrics": {
                "last_validated": "2023-01-01",
                "completeness_score": "95",
                "data_source": "user_input",
                "confidence_score": "high"
            },
            "communication_preferences": {
                "sms_opt_in": "true",
                "email_opt_in": "true",
                "push_opt_in": "false"
            },
            "demographics": {
                "age": "33",
                "age_range": "30-35",
                "gender": "other",
                "location": {
                    "country": "US"
                },
                "mo_location": {
                    "country": "US",
                    "city": "San Francisco",
                    "state": "CA"
                }
            },
            "id": "profile_456",
            "phone": "+1234567890"
        }
        """
        
        let data = json.data(using: .utf8)!
        let decoder = JSONDecoder()
        let response = try decoder.decode(UAUserProfileResponse.self, from: data)
        
        XCTAssertEqual(response.birthDate, "1990-01-01")
        XCTAssertEqual(response.metadata.version, "1.0")
        XCTAssertEqual(response.metadata.sourceSystem, "test")
        XCTAssertEqual(response.email, "<EMAIL>")
        XCTAssertEqual(response.userId, "user_123")
        XCTAssertEqual(response.contentPreferences.interests, ["tech", "science"])
        XCTAssertEqual(response.demographics.age, "33")
        XCTAssertEqual(response.demographics.location.country, "US")
        XCTAssertEqual(response.demographics.moLocation.city, "San Francisco")
    }
    
    func testApiResponseSerialization() throws {
        let transactionResponse = TransactionResponse(
            id: "txn_123",
            type: "subscription",
            userId: "user_456",
            publisherId: "pub_789",
            publisherName: "Test Publisher",
            amount: 9.99,
            interchangeFee: 0.30,
            status: "completed",
            disputeStatus: nil,
            currencyCode: "USD",
            description: "Monthly subscription",
            createdAt: "2023-01-01T00:00:00Z",
            updatedAt: "2023-01-01T00:05:00Z",
            finalizedAt: nil
        )
        
        let apiResponse = ApiResponse(
            data: transactionResponse,
            success: true,
            message: "Success"
        )
        
        let encoder = JSONEncoder()
        let data = try encoder.encode(apiResponse)
        
        let decoder = JSONDecoder()
        let decodedResponse = try decoder.decode(ApiResponse<TransactionResponse>.self, from: data)
        
        XCTAssertEqual(decodedResponse.success, true)
        XCTAssertEqual(decodedResponse.message, "Success")
        XCTAssertEqual(decodedResponse.data?.id, "txn_123")
    }
}
