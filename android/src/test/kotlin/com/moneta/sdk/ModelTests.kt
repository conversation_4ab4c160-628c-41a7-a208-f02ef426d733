package com.moneta.sdk

import com.moneta.sdk.model.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import org.junit.Test
import org.junit.Assert.*

class ModelTests {
    
    private val json = Json { ignoreUnknownKeys = true }
    
    // MARK: - Request Models Tests
    
    @Test
    fun testOnboardingRequestSerialization() {
        val request = OnboardingRequest(
            deviceName = "Test Device",
            deviceToken = "test_token_123",
            publicKey = "test_public_key"
        )
        
        val jsonString = json.encodeToString(request)
        val decodedRequest = json.decodeFromString<OnboardingRequest>(jsonString)
        
        assertEquals(request.deviceName, decodedRequest.deviceName)
        assertEquals(request.deviceToken, decodedRequest.deviceToken)
        assertEquals(request.publicKey, decodedRequest.publicKey)
    }
    
    @Test
    fun testApprovalSubscriptionChargeRequestSerialization() {
        val request = ApprovalSubscriptionChargeRequest(
            approved = true,
            reason = "Test approval"
        )
        
        val jsonString = json.encodeToString(request)
        val decodedRequest = json.decodeFromString<ApprovalSubscriptionChargeRequest>(jsonString)
        
        assertEquals(request.approved, decodedRequest.approved)
        assertEquals(request.reason, decodedRequest.reason)
    }
    
    @Test
    fun testCreateDisputeRequestSerialization() {
        val request = CreateDisputeRequest(
            transactionId = "txn_123",
            reason = "Unauthorized charge",
            itemId = "item_456"
        )
        
        val jsonString = json.encodeToString(request)
        val decodedRequest = json.decodeFromString<CreateDisputeRequest>(jsonString)
        
        assertEquals(request.transactionId, decodedRequest.transactionId)
        assertEquals(request.reason, decodedRequest.reason)
        assertEquals(request.itemId, decodedRequest.itemId)
    }
    
    @Test
    fun testPublisherLoginRequestSerialization() {
        val request = PublisherLoginRequest(session = "session_token_123")
        
        val jsonString = json.encodeToString(request)
        val decodedRequest = json.decodeFromString<PublisherLoginRequest>(jsonString)
        
        assertEquals(request.session, decodedRequest.session)
    }
    
    @Test
    fun testRecommendationsRequestSerialization() {
        val request = RecommendationsRequest(
            userId = "user_123",
            limit = 10,
            offset = 0
        )
        
        val jsonString = json.encodeToString(request)
        val decodedRequest = json.decodeFromString<RecommendationsRequest>(jsonString)
        
        assertEquals(request.userId, decodedRequest.userId)
        assertEquals(request.limit, decodedRequest.limit)
        assertEquals(request.offset, decodedRequest.offset)
    }
    
    @Test
    fun testUpdateUAUserProfileRequestSerialization() {
        val contentPreferences = UAUserContentPreferences(interests = listOf("tech", "science"))
        val request = UpdateUAUserProfileRequest(
            userId = "user_123",
            contentPreferences = contentPreferences
        )
        
        val jsonString = json.encodeToString(request)
        val decodedRequest = json.decodeFromString<UpdateUAUserProfileRequest>(jsonString)
        
        assertEquals(request.userId, decodedRequest.userId)
        assertEquals(request.contentPreferences.interests, decodedRequest.contentPreferences.interests)
    }
    
    // MARK: - Response Models Tests
    
    @Test
    fun testOnboardVerificationResponseDeserialization() {
        val jsonString = """
        {
            "userCode": "USER123",
            "deviceCode": "DEVICE456",
            "tokenEndpoint": "https://api.example.com/token",
            "interval": 5
        }
        """
        
        val response = json.decodeFromString<OnboardVerificationResponse>(jsonString)
        
        assertEquals("USER123", response.userCode)
        assertEquals("DEVICE456", response.deviceCode)
        assertEquals("https://api.example.com/token", response.tokenEndpoint)
        assertEquals(5, response.interval)
    }
    
    @Test
    fun testOnboardUserResponseDeserialization() {
        val jsonString = """
        {
            "id": "user_123",
            "name": "Test User",
            "email": "<EMAIL>",
            "deviceId": "device_456",
            "membershipOrgId": "org_789"
        }
        """
        
        val response = json.decodeFromString<OnboardUserResponse>(jsonString)
        
        assertEquals("user_123", response.id)
        assertEquals("Test User", response.name)
        assertEquals("<EMAIL>", response.email)
        assertEquals("device_456", response.deviceId)
        assertEquals("org_789", response.membershipOrgId)
    }
    
    @Test
    fun testTransactionResponseDeserialization() {
        val jsonString = """
        {
            "id": "txn_123",
            "type": "subscription",
            "userId": "user_456",
            "publisherId": "pub_789",
            "publisherName": "Test Publisher",
            "amount": 9.99,
            "interchangeFee": 0.30,
            "status": "completed",
            "currencyCode": "USD",
            "description": "Monthly subscription",
            "createdAt": "2023-01-01T00:00:00Z",
            "updatedAt": "2023-01-01T00:05:00Z"
        }
        """
        
        val response = json.decodeFromString<TransactionResponse>(jsonString)
        
        assertEquals("txn_123", response.id)
        assertEquals("subscription", response.type)
        assertEquals("user_456", response.userId)
        assertEquals("pub_789", response.publisherId)
        assertEquals("Test Publisher", response.publisherName)
        assertEquals(9.99, response.amount, 0.001)
        assertEquals(0.30, response.interchangeFee, 0.001)
        assertEquals("completed", response.status)
        assertEquals("USD", response.currencyCode)
        assertEquals("Monthly subscription", response.description)
        assertEquals("2023-01-01T00:00:00Z", response.createdAt)
        assertEquals("2023-01-01T00:05:00Z", response.updatedAt)
    }
    
    @Test
    fun testUAUserProfileResponseDeserialization() {
        val jsonString = """
        {
            "birth_date": "1990-01-01",
            "metadata": {
                "version": "1.0",
                "source_system": "test"
            },
            "created_at": "2023-01-01T00:00:00Z",
            "device_ids": ["device1", "device2"],
            "email": "<EMAIL>",
            "language": "en",
            "updated_at": "2023-01-01T00:05:00Z",
            "user_id": "user_123",
            "content_preferences": {
                "interests": ["tech", "science"]
            },
            "quality_metrics": {
                "last_validated": "2023-01-01",
                "completeness_score": "95",
                "data_source": "user_input",
                "confidence_score": "high"
            },
            "communication_preferences": {
                "sms_opt_in": "true",
                "email_opt_in": "true",
                "push_opt_in": "false"
            },
            "demographics": {
                "age": "33",
                "age_range": "30-35",
                "gender": "other",
                "location": {
                    "country": "US"
                },
                "mo_location": {
                    "country": "US",
                    "city": "San Francisco",
                    "state": "CA"
                }
            },
            "id": "profile_456",
            "phone": "+1234567890"
        }
        """
        
        val response = json.decodeFromString<UAUserProfileResponse>(jsonString)
        
        assertEquals("1990-01-01", response.birthDate)
        assertEquals("1.0", response.metadata.version)
        assertEquals("test", response.metadata.sourceSystem)
        assertEquals("<EMAIL>", response.email)
        assertEquals("user_123", response.userId)
        assertEquals(listOf("tech", "science"), response.contentPreferences.interests)
        assertEquals("33", response.demographics.age)
        assertEquals("US", response.demographics.location.country)
        assertEquals("San Francisco", response.demographics.moLocation.city)
    }
    
    @Test
    fun testApiResponseSerialization() {
        val transactionResponse = TransactionResponse(
            id = "txn_123",
            type = "subscription",
            userId = "user_456",
            publisherId = "pub_789",
            publisherName = "Test Publisher",
            amount = 9.99,
            interchangeFee = 0.30,
            status = "completed",
            disputeStatus = null,
            currencyCode = "USD",
            description = "Monthly subscription",
            createdAt = "2023-01-01T00:00:00Z",
            updatedAt = "2023-01-01T00:05:00Z",
            finalizedAt = null
        )
        
        val apiResponse = ApiResponse(
            data = transactionResponse,
            success = true,
            message = "Success"
        )
        
        val jsonString = json.encodeToString(apiResponse)
        val decodedResponse = json.decodeFromString<ApiResponse<TransactionResponse>>(jsonString)
        
        assertEquals(true, decodedResponse.success)
        assertEquals("Success", decodedResponse.message)
        assertEquals("txn_123", decodedResponse.data?.id)
    }
    
    @Test
    fun testPaginatedResponseDeserialization() {
        val jsonString = """
        {
            "data": [
                {
                    "id": "txn_123",
                    "type": "subscription",
                    "userId": "user_456",
                    "publisherId": "pub_789",
                    "publisherName": "Test Publisher",
                    "amount": 9.99,
                    "interchangeFee": 0.30,
                    "status": "completed",
                    "currencyCode": "USD",
                    "description": "Monthly subscription",
                    "createdAt": "2023-01-01T00:00:00Z",
                    "updatedAt": "2023-01-01T00:05:00Z"
                }
            ],
            "pagination": {
                "hasNext": true,
                "hasPrevious": false,
                "nextCursor": "cursor_123",
                "previousCursor": null
            }
        }
        """
        
        val response = json.decodeFromString<PaginatedResponse<TransactionResponse>>(jsonString)
        
        assertEquals(1, response.data.size)
        assertEquals("txn_123", response.data[0].id)
        assertEquals(true, response.pagination.hasNext)
        assertEquals(false, response.pagination.hasPrevious)
        assertEquals("cursor_123", response.pagination.nextCursor)
        assertEquals(null, response.pagination.previousCursor)
    }
}
