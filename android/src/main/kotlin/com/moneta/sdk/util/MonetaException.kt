package com.moneta.sdk.util

sealed class MonetaException(message: String, cause: Throwable? = null) : Exception(message, cause) {
    class NotInitializedException : MonetaException("MonetaSDK is not initialized. Call MonetaSDK.initialize() first.")
    
    class NetworkException(message: String, cause: Throwable? = null) : 
        MonetaException("Network error: $message", cause)
    
    class ApiException(val code: Int, message: String) : 
        MonetaException("API error ($code): $message")
    
    class DecodingException(message: String, cause: Throwable? = null) : 
        MonetaException("Failed to decode response: $message", cause)
    
    class CryptoException(message: String, cause: Throwable? = null) : 
        MonetaException("Cryptography error: $message", cause)
    
    class SecureStorageException(message: String, cause: Throwable? = null) : 
        MonetaException("Secure storage error: $message", cause)
}