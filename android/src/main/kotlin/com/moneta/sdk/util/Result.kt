package com.moneta.sdk.util

sealed class Result<out T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val exception: Exception) : Result<Nothing>()
    
    companion object {
        fun <T> success(data: T): Result<T> = Success(data)
        fun error(exception: Exception): Result<Nothing> = Error(exception)
    }
}

sealed class MonetaException(message: String) : Exception(message) {
    class NetworkException(message: String, cause: Throwable? = null) : 
        MonetaException(message).apply { cause?.let { initCause(it) } }
    
    class ApiException(val code: Int, message: String) : 
        MonetaException("API Error $code: $message")
    
    class DecodingException(message: String, cause: Throwable? = null) : 
        MonetaException(message).apply { cause?.let { initCause(it) } }
    
    class NotInitializedException : 
        MonetaException("MonetaSDK has not been initialized")
}