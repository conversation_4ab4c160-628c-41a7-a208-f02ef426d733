package com.moneta.sdk

import android.content.Context
import com.moneta.sdk.internal.InternalMembershipOrgClient
import com.moneta.sdk.internal.InternalMonetaCoreClient
import com.moneta.sdk.internal.InternalUAClient
import com.moneta.sdk.internal.MembershipOrgSignatureInterceptor
import com.moneta.sdk.internal.SecureStorage
import com.moneta.sdk.internal.InternalNetworkClient
import com.moneta.sdk.model.*
import com.moneta.sdk.util.MonetaException
import com.moneta.sdk.util.Result
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import java.security.KeyPair
import java.security.KeyPairGenerator
import java.security.SecureRandom
import java.util.Base64

object MonetaSDK {
    private var baseUrl: String? = null
    private lateinit var context: Context
    private lateinit var secureStorage: SecureStorage
    private lateinit var okHttpClient: OkHttpClient
    private lateinit var jsonSerializer: Json
    
    // Internal clients
    private lateinit var membershipOrgClient: InternalMembershipOrgClient
    private lateinit var monetaCoreClient: InternalMonetaCoreClient
    private lateinit var uaClient: InternalUAClient
    
    fun initialize(context: Context, baseUrl: String) {
        if (this.baseUrl != null) {
            // Already initialized
            return
        }
        
        this.context = context.applicationContext
        this.baseUrl = baseUrl
        this.secureStorage = SecureStorage(this.context)
        
        // Initialize JSON serializer
        this.jsonSerializer = Json { 
            ignoreUnknownKeys = true 
            isLenient = true
        }
        
        // Initialize OkHttpClient with interceptors
        val signatureInterceptor = MembershipOrgSignatureInterceptor(secureStorage)
        this.okHttpClient = OkHttpClient.Builder()
            .addInterceptor(signatureInterceptor)
            .build()
        
        // Initialize internal clients with appropriate base URLs
        val networkClient = InternalNetworkClient(okHttpClient, jsonSerializer)
        
        membershipOrgClient = InternalMembershipOrgClient(
            baseUrl = "$baseUrl/api/mo",
            networkClient = networkClient
        )
        
        monetaCoreClient = InternalMonetaCoreClient(
            baseUrl = "$baseUrl/users",
            networkClient = networkClient
        )
        
        uaClient = InternalUAClient(
            baseUrl = "$baseUrl/recommendations",
            networkClient = networkClient
        )
    }
    
    private fun checkInitialized() {
        if (baseUrl == null) {
            throw MonetaException.NotInitializedException()
        }
    }
    
    // Public API methods
    
    // Onboarding
    suspend fun startOnboarding(): Result<OnboardVerificationResponse> {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            try {
                // Generate RSA key pair
                val keyPair = generateKeyPair()
                
                // Store private key securely
                val privateKeyEncoded = android.util.Base64.encodeToString(
                    keyPair.private.encoded, 
                    android.util.Base64.DEFAULT
                )
                secureStorage.saveData("private_key", privateKeyEncoded)
                
                // Create device token (in a real app, this would be a Firebase token or similar)
                val deviceToken = generateDeviceToken()
                secureStorage.saveData("device_token", deviceToken)
                
                // Create onboarding request
                val request = OnboardingRequest(
                    deviceName = android.os.Build.MODEL,
                    deviceToken = deviceToken,
                    publicKey = android.util.Base64.encodeToString(
                        keyPair.public.encoded, 
                        android.util.Base64.DEFAULT
                    )
                )
                
                // Make API call
                val response = membershipOrgClient.startOnboarding(request)
                if (response.success && response.data != null) {
                    Result.success(response.data)
                } else {
                    Result.error(MonetaException.ApiException(
                        400, 
                        response.message ?: "Unknown error"
                    ))
                }
            } catch (e: Exception) {
                Result.error(e)
            }
        }
    }
    
    suspend fun completeOnboarding(): Result<OnboardUserResponse> {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            try {
                val response = membershipOrgClient.completeOnboarding()
                if (response.success && response.data != null) {
                    // Store onboarding data
                    secureStorage.saveData("onboarding_data", jsonSerializer.encodeToString(response.data))
                    Result.success(response.data)
                } else {
                    Result.error(MonetaException.ApiException(
                        400, 
                        response.message ?: "Unknown error"
                    ))
                }
            } catch (e: Exception) {
                Result.error(e)
            }
        }
    }
    
    // Transaction methods
    suspend fun getTransaction(transactionId: String): Result<TransactionResponse> {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            try {
                val response = monetaCoreClient.getTransaction(transactionId)
                if (response.success && response.data != null) {
                    Result.success(response.data)
                } else {
                    Result.error(MonetaException.ApiException(
                        400, 
                        response.message ?: "Unknown error"
                    ))
                }
            } catch (e: Exception) {
                Result.error(e)
            }
        }
    }
    
    suspend fun approveSubscriptionCharge(transactionId: String, status: String): Result<TransactionResponse> {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            try {
                val request = ApprovalSubscriptionChargeRequest(status)
                val response = monetaCoreClient.approveSubscriptionCharge(transactionId, request)
                if (response.success && response.data != null) {
                    Result.success(response.data)
                } else {
                    Result.error(MonetaException.ApiException(
                        400, 
                        response.message ?: "Unknown error"
                    ))
                }
            } catch (e: Exception) {
                Result.error(e)
            }
        }
    }
    
    suspend fun createDispute(transactionId: String, reason: String, itemId: String? = null): Result<Boolean> {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            try {
                val request = CreateDisputeRequest(transactionId, reason, itemId)
                val response = monetaCoreClient.createDispute(request)
                Result.success(response.success)
            } catch (e: Exception) {
                Result.error(e)
            }
        }
    }
    
    // UA Client methods
    suspend fun getRecommendations(userId: String, userProfile: UserProfile, offset: Int, limit: Int): Result<List<Any>> {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            try {
                val request = RecommendationsRequest(userId, userProfile, offset, limit)
                val response = uaClient.getRecommendations(request)
                if (response.success && response.data != null) {
                    Result.success(response.data)
                } else {
                    Result.error(MonetaException.ApiException(
                        400, 
                        response.message ?: "Unknown error"
                    ))
                }
            } catch (e: Exception) {
                Result.error(e)
            }
        }
    }
    
    suspend fun updateUserProfile(userId: String, contentPreferences: ContentPreferences): Result<Boolean> {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            try {
                val request = UpdateUAUserProfileRequest(userId, contentPreferences)
                val response = uaClient.updateUserProfile(request)
                Result.success(response.success)
            } catch (e: Exception) {
                Result.error(e)
            }
        }
    }
    
    // Publisher authentication
    suspend fun authPublisher(qrCodeId: String, session: String): Result<OnboardUserResponse> {
        checkInitialized()
        return withContext(Dispatchers.IO) {
            try {
                val response = membershipOrgClient.publisherLogin(session)
                if (response.success && response.data != null) {
                    Result.success(response.data)
                } else {
                    Result.error(MonetaException.ApiException(
                        400, 
                        response.message ?: "Unknown error"
                    ))
                }
            } catch (e: Exception) {
                Result.error(e)
            }
        }
    }
    
    // Helper methods
    private fun generateKeyPair(): KeyPair {
        val keyPairGenerator = KeyPairGenerator.getInstance("RSA")
        keyPairGenerator.initialize(2048, SecureRandom())
        return keyPairGenerator.generateKeyPair()
    }
    
    private fun generateDeviceToken(): String {
        // In a real implementation, this would be a Firebase token or similar
        // For demo purposes, generate a random string
        val random = SecureRandom()
        val bytes = ByteArray(32)
        random.nextBytes(bytes)
        return android.util.Base64.encodeToString(bytes, android.util.Base64.DEFAULT)
    }
}
