package com.moneta.sdk.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class OnboardingRequest(
    @SerialName("deviceName") val deviceName: String,
    @SerialName("deviceToken") val deviceToken: String,
    @SerialName("publicKey") val publicKey: String
)

@Serializable
data class ApprovalSubscriptionChargeRequest(
    @SerialName("status") val status: String // enum: approved, rejected
)

@Serializable
data class CreateDisputeRequest(
    @SerialName("transactionId") val transactionId: String,
    @SerialName("reason") val reason: String,
    @SerialName("itemId") val itemId: String? = null
)

@Serializable
data class PublisherLoginRequest(
    @SerialName("pinet_session") val session: String
)

@Serializable
data class RecommendationsRequest(
    @SerialName("user_id") val userId: String,
    @SerialName("user_profile") val userProfile: UserProfile,
    @SerialName("offset") val offset: Int,
    @SerialName("limit") val limit: Int
)

@Serializable
data class UserProfile(
    @SerialName("demographics") val demographics: Map<String, JsonElement> = emptyMap(),
    @SerialName("content_preferences") val contentPreferences: ContentPreferences
)

@Serializable
data class ContentPreferences(
    @SerialName("interests") val interests: List<String>
)

@Serializable
data class UpdateUAUserProfileRequest(
    @SerialName("user_id") val userId: String,
    @SerialName("content_preferences") val contentPreferences: ContentPreferences
)
