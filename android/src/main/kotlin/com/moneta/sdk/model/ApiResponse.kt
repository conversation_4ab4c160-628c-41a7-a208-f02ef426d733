package com.moneta.sdk.model

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class ApiResponse<T>(
    val data: T? = null, // JSON Key: "data"
    val success: Boolean, // JSON Key: "success"
    val message: String? = null // JSON Key: "message"
)

@Serializable
data class PaginatedResponse<T>(
    val content: List<T>, // JSON Key: "content"
    val pageNumber: Int, // JSON Key: "pageNumber"
    val pageSize: Int, // JSON Key: "pageSize"
    val totalElements: Long, // JSON Key: "totalElements"
    val totalPages: Int // JSON Key: "totalPages"
)