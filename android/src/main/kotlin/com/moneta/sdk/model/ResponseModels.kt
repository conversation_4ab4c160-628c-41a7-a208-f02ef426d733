package com.moneta.sdk.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OnboardVerificationResponse(
    @SerialName("userCode") val userCode: String,
    @SerialName("deviceCode") val deviceCode: String,
    @SerialName("tokenEndpoint") val tokenEndpoint: String,
    @SerialName("interval") val interval: Int
)

@Serializable
data class OnboardUserResponse(
    @SerialName("id") val id: String,
    @SerialName("name") val name: String,
    @SerialName("email") val email: String,
    @SerialName("deviceId") val deviceId: String,
    @SerialName("membershipOrgId") val membershipOrgId: String
)

@Serializable
data class TransactionResponse(
    @SerialName("id") val id: String,
    @SerialName("type") val type: String,
    @SerialName("userId") val userId: String,
    @SerialName("publisherId") val publisherId: String,
    // Additional fields as needed
    @SerialName("amount") val amount: Double? = null,
    @SerialName("currency") val currency: String? = null,
    @SerialName("status") val status: String? = null,
    @SerialName("createdAt") val createdAt: String? = null
)

@Serializable
data class PublisherLoginResponse(
    @SerialName("user") val user: OnboardUserResponse
)

@Serializable
data class UAResponse(
    @SerialName("recommendations") val recommendations: List<UARecommendation>
)

@Serializable
data class UARecommendation(
    @SerialName("id") val id: String,
    @SerialName("title") val title: String,
    @SerialName("url") val url: String,
    @SerialName("source") val source: String,
    @SerialName("publishedAt") val publishedAt: String
)