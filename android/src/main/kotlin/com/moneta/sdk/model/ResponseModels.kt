package com.moneta.sdk.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class OnboardVerificationResponse(
    @SerialName("userCode") val userCode: String,
    @SerialName("deviceCode") val deviceCode: String,
    @SerialName("tokenEndpoint") val tokenEndpoint: String,
    @SerialName("interval") val interval: Int
)

@Serializable
data class OnboardUserResponse(
    @SerialName("id") val id: String,
    @SerialName("name") val name: String,
    @SerialName("email") val email: String,
    @SerialName("deviceId") val deviceId: String,
    @SerialName("membershipOrgId") val membershipOrgId: String
)

@Serializable
data class TransactionResponse(
    @SerialName("id") val id: String,
    @SerialName("type") val type: String,
    @SerialName("userId") val userId: String,
    @SerialName("publisherId") val publisherId: String,
    @SerialName("publisherName") val publisherName: String,
    @SerialName("amount") val amount: Double,
    @SerialName("interchangeFee") val interchangeFee: Double,
    @SerialName("status") val status: String,
    @SerialName("disputeStatus") val disputeStatus: String? = null,
    @SerialName("currencyCode") val currencyCode: String,
    @SerialName("description") val description: String,
    @SerialName("createdAt") val createdAt: String,
    @SerialName("updatedAt") val updatedAt: String,
    @SerialName("finalizedAt") val finalizedAt: String? = null
)

@Serializable
data class UserFeedResponse(
    @SerialName("id") val id: String,
    @SerialName("userId") val userId: String,
    @SerialName("title") val title: String,
    @SerialName("body") val body: String,
    @SerialName("type") val type: String,
    @SerialName("createdAt") val createdAt: String? = null,
    @SerialName("readAt") val readAt: String? = null,
    @SerialName("approvalId") val approvalId: String? = null,
    @SerialName("approvalStatus") val approvalStatus: String? = null,
    @SerialName("approvalAt") val approvalAt: String? = null,
    @SerialName("item") val item: Map<String, JsonElement>? = null
)

@Serializable
data class UAUserProfileResponse(
    @SerialName("birth_date") val birthDate: String,
    @SerialName("metadata") val metadata: UAUserMetadata,
    @SerialName("created_at") val createdAt: String,
    @SerialName("device_ids") val deviceIds: List<String>,
    @SerialName("email") val email: String,
    @SerialName("language") val language: String,
    @SerialName("updated_at") val updatedAt: String,
    @SerialName("user_id") val userId: String,
    @SerialName("content_preferences") val contentPreferences: UAUserContentPreferences,
    @SerialName("quality_metrics") val qualityMetrics: UAUserQualityMetrics,
    @SerialName("communication_preferences") val communicationPreferences: UAUserCommunicationPreferences,
    @SerialName("demographics") val demographics: UAUserDemographics,
    @SerialName("id") val id: String,
    @SerialName("phone") val phone: String,
    @SerialName("moneta_user_id") val monetaUserId: String? = null
)

@Serializable
data class UAUserMetadata(
    @SerialName("version") val version: String,
    @SerialName("source_system") val sourceSystem: String
)

@Serializable
data class UAUserContentPreferences(
    @SerialName("interests") val interests: List<String>
)

@Serializable
data class UAUserQualityMetrics(
    @SerialName("last_validated") val lastValidated: String,
    @SerialName("completeness_score") val completenessScore: String,
    @SerialName("data_source") val dataSource: String,
    @SerialName("confidence_score") val confidenceScore: String
)

@Serializable
data class UAUserCommunicationPreferences(
    @SerialName("sms_opt_in") val smsOptIn: String,
    @SerialName("email_opt_in") val emailOptIn: String,
    @SerialName("push_opt_in") val pushOptIn: String
)

@Serializable
data class UAUserDemographics(
    @SerialName("age") val age: String,
    @SerialName("age_range") val ageRange: String,
    @SerialName("gender") val gender: String,
    @SerialName("income_range") val incomeRange: String? = null,
    @SerialName("location") val location: UAUserLocation,
    @SerialName("mo_location") val moLocation: UAUserMOLocation,
    @SerialName("occupation") val occupation: String? = null
)

@Serializable
data class UAUserLocation(
    @SerialName("country") val country: String
)

@Serializable
data class UAUserMOLocation(
    @SerialName("country") val country: String,
    @SerialName("city") val city: String,
    @SerialName("state") val state: String
)

@Serializable
data class PublisherLoginResponse(
    @SerialName("user") val user: OnboardUserResponse
)

@Serializable
data class UAResponse(
    @SerialName("recommendations") val recommendations: List<UARecommendation>
)

@Serializable
data class UARecommendation(
    @SerialName("id") val id: String,
    @SerialName("title") val title: String,
    @SerialName("url") val url: String,
    @SerialName("source") val source: String,
    @SerialName("publishedAt") val publishedAt: String
)

// Additional Response Models
@Serializable
data class UserPolicyTypeResponse(
    @SerialName("type") val type: String,
    @SerialName("name") val name: String,
    @SerialName("description") val description: String,
    @SerialName("enabled") val enabled: Boolean
)

@Serializable
data class UserPolicyDataResponse(
    @SerialName("type") val type: String,
    @SerialName("data") val data: Map<String, JsonElement>
)

@Serializable
data class IndustryResponse(
    @SerialName("id") val id: String,
    @SerialName("name") val name: String,
    @SerialName("code") val code: String
)

@Serializable
data class IncrementResponse(
    @SerialName("id") val id: String,
    @SerialName("transactionId") val transactionId: String,
    @SerialName("amount") val amount: Double,
    @SerialName("description") val description: String,
    @SerialName("timestamp") val timestamp: String
)

@Serializable
data class ConsumptionActivityStatusResponse(
    @SerialName("requestId") val requestId: String,
    @SerialName("status") val status: String,
    @SerialName("progress") val progress: Int,
    @SerialName("message") val message: String? = null
)

@Serializable
data class ConsumptionActivityResponse(
    @SerialName("id") val id: String,
    @SerialName("transactionId") val transactionId: String,
    @SerialName("activity") val activity: String,
    @SerialName("timestamp") val timestamp: String,
    @SerialName("metadata") val metadata: Map<String, JsonElement>? = null
)

@Serializable
data class DisputeRequestResponse(
    @SerialName("id") val id: String,
    @SerialName("transactionId") val transactionId: String,
    @SerialName("reason") val reason: String,
    @SerialName("status") val status: String,
    @SerialName("createdAt") val createdAt: String,
    @SerialName("updatedAt") val updatedAt: String,
    @SerialName("itemId") val itemId: String? = null
)

@Serializable
data class UserBillingCyclesResponse(
    @SerialName("code") val code: String,
    @SerialName("billingFrom") val billingFrom: String,
    @SerialName("billingTo") val billingTo: String,
    @SerialName("status") val status: String,
    @SerialName("totalAmount") val totalAmount: Double,
    @SerialName("currency") val currency: String
)

@Serializable
data class ConsumptionResponse(
    @SerialName("id") val id: String,
    @SerialName("billingCycleCode") val billingCycleCode: String,
    @SerialName("publisherId") val publisherId: String,
    @SerialName("publisherName") val publisherName: String,
    @SerialName("amount") val amount: Double,
    @SerialName("currency") val currency: String,
    @SerialName("consumptionDate") val consumptionDate: String
)

@Serializable
data class UserBalanceResponse(
    @SerialName("balance") val balance: Double,
    @SerialName("currency") val currency: String,
    @SerialName("lastUpdated") val lastUpdated: String,
    @SerialName("pendingAmount") val pendingAmount: Double? = null
)

@Serializable
data class ArticleResponse(
    @SerialName("id") val id: String,
    @SerialName("title") val title: String,
    @SerialName("content") val content: String,
    @SerialName("category") val category: String,
    @SerialName("publishedAt") val publishedAt: String,
    @SerialName("author") val author: String? = null,
    @SerialName("url") val url: String? = null
)

@Serializable
data class InterestsResponse(
    @SerialName("interests") val interests: List<String>,
    @SerialName("categories") val categories: List<String>
)

@Serializable
data class UpdateUAUserProfileResponse(
    @SerialName("success") val success: Boolean,
    @SerialName("message") val message: String? = null,
    @SerialName("updatedAt") val updatedAt: String? = null
)