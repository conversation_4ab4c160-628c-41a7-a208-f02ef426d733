package com.moneta.sdk.internal

import com.moneta.sdk.model.ApiResponse
import com.moneta.sdk.model.PaginatedResponse
import com.moneta.sdk.util.MonetaException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException

internal class InternalNetworkClient(
    private val okHttpClient: OkHttpClient,
    private val json: Json
) {
    private val jsonMediaType = "application/json; charset=utf-8".toMediaType()
    
    suspend inline fun <reified T> get(url: String, headers: Map<String, String> = emptyMap()): ApiResponse<T> {
        return executeRequest {
            val requestBuilder = Request.Builder().url(url).get()
            headers.forEach { (key, value) -> requestBuilder.addHeader(key, value) }
            requestBuilder.build()
        }
    }
    
    suspend inline fun <reified T, reified R> post(
        url: String, 
        body: T, 
        headers: Map<String, String> = emptyMap()
    ): ApiResponse<R> {
        return executeRequest {
            val jsonBody = json.encodeToString(body)
            val requestBody = jsonBody.toRequestBody(jsonMediaType)
            val requestBuilder = Request.Builder().url(url).post(requestBody)
            headers.forEach { (key, value) -> requestBuilder.addHeader(key, value) }
            requestBuilder.build()
        }
    }
    
    suspend inline fun <reified T, reified R> put(
        url: String, 
        body: T, 
        headers: Map<String, String> = emptyMap()
    ): ApiResponse<R> {
        return executeRequest {
            val jsonBody = json.encodeToString(body)
            val requestBody = jsonBody.toRequestBody(jsonMediaType)
            val requestBuilder = Request.Builder().url(url).put(requestBody)
            headers.forEach { (key, value) -> requestBuilder.addHeader(key, value) }
            requestBuilder.build()
        }
    }
    
    suspend inline fun <reified T> delete(
        url: String, 
        headers: Map<String, String> = emptyMap()
    ): ApiResponse<T> {
        return executeRequest {
            val requestBuilder = Request.Builder().url(url).delete()
            headers.forEach { (key, value) -> requestBuilder.addHeader(key, value) }
            requestBuilder.build()
        }
    }
    
    suspend inline fun <reified T> executeRequest(crossinline requestBuilder: () -> Request): ApiResponse<T> {
        return withContext(Dispatchers.IO) {
            try {
                val request = requestBuilder()
                val response = okHttpClient.newCall(request).execute()
                
                val responseBody = response.body?.string() ?: throw MonetaException.NetworkException("Empty response body")
                
                if (!response.isSuccessful) {
                    throw MonetaException.ApiException(
                        response.code, 
                        "API error: ${response.message}"
                    )
                }
                
                try {
                    json.decodeFromString<ApiResponse<T>>(responseBody)
                } catch (e: Exception) {
                    throw MonetaException.DecodingException("Failed to decode response", e)
                }
            } catch (e: IOException) {
                throw MonetaException.NetworkException("Network error", e)
            }
        }
    }
}