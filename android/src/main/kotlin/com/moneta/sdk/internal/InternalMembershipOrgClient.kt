package com.moneta.sdk.internal

import com.moneta.sdk.model.ApiResponse
import com.moneta.sdk.model.OnboardingRequest
import com.moneta.sdk.model.OnboardVerificationResponse
import com.moneta.sdk.model.OnboardUserResponse

internal class InternalMembershipOrgClient(
    private val baseUrl: String,
    private val networkClient: InternalNetworkClient
) {
    suspend fun startOnboarding(request: OnboardingRequest): ApiResponse<OnboardVerificationResponse> {
        return networkClient.post("$baseUrl/onboard/verify", request)
    }
    
    suspend fun completeOnboarding(): ApiResponse<OnboardUserResponse> {
        return networkClient.get("$baseUrl/onboard/user")
    }
    
    suspend fun publisherLogin(session: String): ApiResponse<OnboardUserResponse> {
        val request = mapOf("pinet_session" to session)
        return networkClient.post("$baseUrl/publisher/login", request)
    }
}