package com.moneta.sdk.internal

import com.moneta.sdk.model.*
import kotlinx.serialization.json.JsonElement

internal class InternalMembershipOrgClient(
    private val baseUrl: String,
    private val networkClient: InternalNetworkClient
) {
    // Onboarding endpoints
    suspend fun fetchMoInfo(): ApiResponse<JsonElement> {
        return networkClient.get("$baseUrl/info")
    }

    suspend fun verifyOnboarding(requestId: String, request: OnboardingRequest): ApiResponse<OnboardVerificationResponse> {
        return networkClient.post("$baseUrl/moneta-pass/onboard/device/$requestId/code", request)
    }

    suspend fun checkUserCodeIsVerified(requestId: String, deviceCode: String): ApiResponse<OnboardUserResponse> {
        return networkClient.get("$baseUrl/moneta-pass/onboard/device/$requestId/token?deviceCode=$deviceCode")
    }

    // Legacy methods for backward compatibility
    suspend fun startOnboarding(request: OnboardingRequest): ApiResponse<OnboardVerificationResponse> {
        return networkClient.post("$baseUrl/onboard/verify", request)
    }

    suspend fun completeOnboarding(): ApiResponse<OnboardUserResponse> {
        return networkClient.get("$baseUrl/onboard/user")
    }

    suspend fun publisherLogin(session: String): ApiResponse<OnboardUserResponse> {
        val request = mapOf("pinet_session" to session)
        return networkClient.post("$baseUrl/publisher/login", request)
    }

    // Transaction endpoints
    suspend fun fetchTransactions(
        month: String? = null,
        pageSize: Int? = null,
        pageBefore: String? = null,
        pageAfter: String? = null
    ): ApiResponse<PaginatedResponse<TransactionResponse>> {
        val params = mutableListOf<String>()
        month?.let { params.add("month=$it") }
        pageSize?.let { params.add("page[size]=$it") }
        pageBefore?.let { params.add("page[before]=$it") }
        pageAfter?.let { params.add("page[after]=$it") }

        val queryString = if (params.isNotEmpty()) "?" + params.joinToString("&") else ""
        return networkClient.get("$baseUrl/user/transactions$queryString")
    }

    suspend fun getTransaction(id: String): ApiResponse<TransactionResponse> {
        return networkClient.get("$baseUrl/user/transaction/$id")
    }

    suspend fun getRelatedTransactions(
        id: String,
        pageSize: Int? = null,
        pageOrder: String? = null
    ): ApiResponse<PaginatedResponse<TransactionResponse>> {
        val params = mutableListOf<String>()
        pageSize?.let { params.add("page[size]=$it") }
        pageOrder?.let { params.add("page[order]=$it") }

        val queryString = if (params.isNotEmpty()) "?" + params.joinToString("&") else ""
        return networkClient.get("$baseUrl/user/transactions/$id/related-transactions$queryString")
    }

    // User Feed endpoints
    suspend fun fetchUserFeeds(
        pageSize: Int? = null,
        pageAfter: String? = null
    ): ApiResponse<PaginatedResponse<UserFeedResponse>> {
        val params = mutableListOf<String>()
        pageSize?.let { params.add("page[size]=$it") }
        pageAfter?.let { params.add("page[after]=$it") }

        val queryString = if (params.isNotEmpty()) "?" + params.joinToString("&") else ""
        return networkClient.get("$baseUrl/user/feeds$queryString")
    }

    suspend fun getFeedById(feedId: String): ApiResponse<UserFeedResponse> {
        return networkClient.get("$baseUrl/user/feeds/$feedId")
    }

    suspend fun markFeedsAsRead(body: Map<String, List<String>>): ApiResponse<JsonElement> {
        return networkClient.post("$baseUrl/user/feeds/mark-as-read", body)
    }

    suspend fun deleteFeeds(body: Map<String, List<String>>): ApiResponse<JsonElement> {
        return networkClient.delete("$baseUrl/user/feeds", body)
    }

    // Device management
    suspend fun deviceRemoval(deviceId: String): ApiResponse<JsonElement> {
        return networkClient.delete("$baseUrl/user/devices/$deviceId")
    }

    // Policy endpoints
    suspend fun fetchPolicies(): ApiResponse<List<UserPolicyTypeResponse>> {
        return networkClient.get("$baseUrl/user/policies")
    }

    suspend fun setPolicy(type: String, body: Map<String, JsonElement>): ApiResponse<UserPolicyDataResponse> {
        return networkClient.post("$baseUrl/user/policies/$type", body)
    }

    suspend fun fetchIndustries(): ApiResponse<List<IndustryResponse>> {
        return networkClient.get("$baseUrl/user/policies/params/industries")
    }

    suspend fun fetchPublishers(): ApiResponse<List<IndustryResponse>> {
        return networkClient.get("$baseUrl/user/policies/params/publishers")
    }

    // Approval endpoints
    suspend fun processSubscriptionCharge(id: String, request: ApprovalSubscriptionChargeRequest): ApiResponse<JsonElement> {
        return networkClient.post("$baseUrl/user/approvals/$id", request)
    }

    // Transaction details
    suspend fun fetchIncrements(id: String): ApiResponse<PaginatedResponse<IncrementResponse>> {
        return networkClient.get("$baseUrl/user/transactions/$id/items")
    }

    // Consumption activities
    suspend fun createFetchingConsumptionActivitiesRequest(id: String): ApiResponse<ConsumptionActivityStatusResponse> {
        return networkClient.post("$baseUrl/user/transactions/$id/consumption-activities/request", emptyMap<String, String>())
    }

    suspend fun checkStatusFetchingConsumptionActivitiesRequest(id: String, rid: String): ApiResponse<ConsumptionActivityStatusResponse> {
        return networkClient.get("$baseUrl/user/transactions/$id/consumption-activities/request/$rid")
    }

    suspend fun fetchConsumptionActivities(id: String): ApiResponse<List<ConsumptionActivityResponse>> {
        return networkClient.get("$baseUrl/user/transactions/$id/consumption-activities")
    }

    // Dispute endpoints
    suspend fun createDisputeRequest(request: CreateDisputeRequest): ApiResponse<DisputeRequestResponse> {
        return networkClient.post("$baseUrl/user/disputes", request)
    }

    suspend fun fetchDisputes(transactionId: String? = null): ApiResponse<List<DisputeRequestResponse>> {
        val queryString = transactionId?.let { "?transactionId=$it" } ?: ""
        return networkClient.get("$baseUrl/user/disputes$queryString")
    }

    // Settlement endpoints
    suspend fun fetchLatestSettlementBillingCycle(): ApiResponse<UserBillingCyclesResponse> {
        return networkClient.get("$baseUrl/user/settlement/billing-cycles/latest")
    }

    suspend fun fetchSettlementBillingCycle(
        billingFrom: String? = null,
        billingTo: String? = null
    ): ApiResponse<List<UserBillingCyclesResponse>> {
        val params = mutableListOf<String>()
        billingFrom?.let { params.add("billingFrom=$it") }
        billingTo?.let { params.add("billingTo=$it") }

        val queryString = if (params.isNotEmpty()) "?" + params.joinToString("&") else ""
        return networkClient.get("$baseUrl/user/settlement/billing-cycles$queryString")
    }

    suspend fun fetchSettlementBillingCycleConsumption(code: String): ApiResponse<List<ConsumptionResponse>> {
        return networkClient.get("$baseUrl/user/settlement/billing-cycles/$code/consumption")
    }

    // Balance endpoints
    suspend fun fetchBalance(): ApiResponse<UserBalanceResponse> {
        return networkClient.get("$baseUrl/user/profile/balance")
    }

    suspend fun fetchDebitHistory(): ApiResponse<JsonElement> {
        return networkClient.get("$baseUrl/user/profile/debit-history")
    }
}