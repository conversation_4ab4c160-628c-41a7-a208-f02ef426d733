package com.moneta.sdk.internal

import com.moneta.sdk.model.*

internal class InternalMonetaCoreClient(
    private val baseUrl: String,
    private val networkClient: InternalNetworkClient
) {
    // Publisher authentication endpoints
    suspend fun authPublisher(qrCodeId: String, request: PublisherLoginRequest): ApiResponse<PublisherLoginResponse> {
        return networkClient.post("$baseUrl/sign_in/authentication_qr_codes/$qrCodeId/submit", request)
    }

    suspend fun checkPublisherLoginStatus(qrCodeId: String): ApiResponse<PublisherLoginResponse> {
        return networkClient.get("$baseUrl/sign_in/authentication_qr_codes/$qrCodeId")
    }

    // Article endpoints
    suspend fun fetchArticles(
        categories: String? = null,
        before: String? = null
    ): ApiResponse<List<ArticleResponse>> {
        val params = mutableListOf<String>()
        categories?.let { params.add("categories=$it") }
        before?.let { params.add("before=$it") }

        val queryString = if (params.isNotEmpty()) "?" + params.joinToString("&") else ""
        return networkClient.get("$baseUrl/api/v1/articles$queryString")
    }

    suspend fun fetchArticleCategories(): ApiResponse<List<String>> {
        return networkClient.get("$baseUrl/api/v1/article_categories")
    }

    // Legacy methods for backward compatibility
    suspend fun getTransactions(page: Int, size: Int): ApiResponse<PaginatedResponse<TransactionResponse>> {
        return networkClient.get("$baseUrl/transactions?page=$page&size=$size")
    }

    suspend fun getTransaction(transactionId: String): ApiResponse<TransactionResponse> {
        return networkClient.get("$baseUrl/transactions/$transactionId")
    }

    suspend fun approveSubscriptionCharge(
        transactionId: String,
        request: ApprovalSubscriptionChargeRequest
    ): ApiResponse<TransactionResponse> {
        return networkClient.put("$baseUrl/transactions/$transactionId/approve", request)
    }

    suspend fun createDispute(request: CreateDisputeRequest): ApiResponse<Boolean> {
        return networkClient.post("$baseUrl/disputes", request)
    }
}
