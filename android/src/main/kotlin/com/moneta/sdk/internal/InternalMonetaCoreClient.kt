package com.moneta.sdk.internal

import com.moneta.sdk.model.*

internal class InternalMonetaCoreClient(
    private val baseUrl: String,
    private val networkClient: InternalNetworkClient
) {
    suspend fun getTransactions(page: Int, size: Int): ApiResponse<PaginatedResponse<TransactionResponse>> {
        return networkClient.get("$baseUrl/transactions?page=$page&size=$size")
    }
    
    suspend fun getTransaction(transactionId: String): ApiResponse<TransactionResponse> {
        return networkClient.get("$baseUrl/transactions/$transactionId")
    }
    
    suspend fun approveSubscriptionCharge(
        transactionId: String, 
        request: ApprovalSubscriptionChargeRequest
    ): ApiResponse<TransactionResponse> {
        return networkClient.put("$baseUrl/transactions/$transactionId/approve", request)
    }
    
    suspend fun createDispute(request: CreateDisputeRequest): ApiResponse<Boolean> {
        return networkClient.post("$baseUrl/disputes", request)
    }
}
