package com.moneta.sdk.internal

import com.moneta.sdk.model.*

internal class InternalUAClient(
    private val baseUrl: String,
    private val networkClient: InternalNetworkClient
) {
    suspend fun getRecommendations(request: RecommendationsRequest): ApiResponse<List<Any>> {
        return networkClient.post("$baseUrl/recommendations", request)
    }
    
    suspend fun updateUserProfile(request: UpdateUAUserProfileRequest): ApiResponse<Boolean> {
        return networkClient.put("$baseUrl/user-profile", request)
    }
}
