package com.moneta.sdk.internal

import com.moneta.sdk.model.*

internal class InternalUAClient(
    private val baseUrl: String,
    private val networkClient: InternalNetworkClient
) {
    // Recommendation endpoints
    suspend fun fetchRssContent(
        userId: String,
        limit: Int? = null,
        offset: Int? = null
    ): ApiResponse<UAResponse> {
        val params = mutableListOf<String>()
        limit?.let { params.add("limit=$it") }
        offset?.let { params.add("offset=$it") }

        val queryString = if (params.isNotEmpty()) "?" + params.joinToString("&") else ""
        return networkClient.get("$baseUrl/recommendations/$userId$queryString")
    }

    suspend fun fetchRecommendations(request: RecommendationsRequest): ApiResponse<UAResponse> {
        return networkClient.post("$baseUrl/recommendations", request)
    }

    // Interest endpoints
    suspend fun fetchInterests(): ApiResponse<InterestsResponse> {
        return networkClient.get("$baseUrl/interests")
    }

    // User profile endpoints
    suspend fun fetchUserProfile(userId: String): ApiResponse<UAUserProfileResponse> {
        return networkClient.get("$baseUrl/user-profile/$userId")
    }

    suspend fun updateUserProfile(request: UpdateUAUserProfileRequest): ApiResponse<UpdateUAUserProfileResponse> {
        return networkClient.put("$baseUrl/user-profile", request)
    }

    // Legacy methods for backward compatibility
    suspend fun getRecommendations(request: RecommendationsRequest): ApiResponse<List<Any>> {
        return networkClient.post("$baseUrl/recommendations", request)
    }
}
