package com.moneta.sdk.internal

import android.util.Base64
import com.moneta.sdk.model.OnboardUserResponse
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import okio.Buffer
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.util.Date

internal class MembershipOrgSignatureInterceptor(
    private val secureStorage: SecureStorage
) : Interceptor {
    
    companion object {
        private const val REQUIRES_SIGNATURE_EXTRA = "RequiresSignature"
        private const val HEADER_CLIENT_ID = "Client-ID"
        private const val HEADER_SIGNATURE = "Signature"
        private const val HEADER_REQUEST_TIME = "Request-Time"
        private const val SIGNATURE_ALGORITHM = "RSA-SHA256"
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // Check if this request requires a signature
        if (requiresSignature(originalRequest)) {
            val onboardingData = secureStorage.getData("onboarding_data")
                ?: return chain.proceed(originalRequest) // Proceed without signature if no onboarding data
            
            val privateKeyString = secureStorage.getData("private_key")
                ?: return chain.proceed(originalRequest) // Proceed without signature if no private key
            
            // Extract client ID from onboarding data
            val clientId = try {
                val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
                val onboardingResponse = json.decodeFromString<OnboardUserResponse>(onboardingData)
                onboardingResponse.deviceId
            } catch (e: Exception) {
                "unknown_client_id" // Fallback if parsing fails
            }
            
            // Generate timestamp
            val timestamp = Date().time.toString()
            
            // Get request details
            val method = originalRequest.method
            val uri = originalRequest.url.toString()
            val body = originalRequest.body?.let { requestBody ->
                try {
                    val buffer = okio.Buffer()
                    requestBody.writeTo(buffer)
                    buffer.readUtf8()
                } catch (e: Exception) {
                    "" // Empty body if reading fails
                }
            } ?: ""
            
            // Generate signature
            val signature = generateSignature(method, uri, clientId, timestamp, body, privateKeyString)
            
            // Create a new request with the required headers
            val newRequest = originalRequest.newBuilder()
                .header(HEADER_CLIENT_ID, clientId)
                .header(HEADER_SIGNATURE, "signature=$signature, algorithm=$SIGNATURE_ALGORITHM")
                .header(HEADER_REQUEST_TIME, timestamp)
                .build()
            
            return chain.proceed(newRequest)
        }
        
        return chain.proceed(originalRequest)
    }
    
    private fun requiresSignature(request: Request): Boolean {
        // Check for the RequiresSignature tag in request
        val requiresSignatureTag = request.tag(String::class.java)
        if (requiresSignatureTag == REQUIRES_SIGNATURE_EXTRA) {
            return true
        }

        // Fallback: check URL patterns that typically require signatures
        val url = request.url.toString()
        return url.contains("/api/mo/") ||
               url.contains("/user/") ||
               url.contains("/onboard/") ||
               url.contains("/publisher/") ||
               url.contains("/policies/") ||
               url.contains("/transactions/") ||
               url.contains("/feeds/") ||
               url.contains("/disputes/") ||
               url.contains("/settlement/") ||
               url.contains("/approvals/")
    }
    
    private fun generateSignature(
        method: String,
        uri: String,
        clientId: String,
        timestamp: String,
        body: String,
        privateKeyString: String
    ): String {
        // Construct the canonical string
        val canonicalString = "$method\n$uri\n$clientId\n$timestamp\n$body"
        
        // Convert private key string to PrivateKey object
        val privateKeyBytes = Base64.decode(privateKeyString, Base64.DEFAULT)
        val keySpec = PKCS8EncodedKeySpec(privateKeyBytes)
        val keyFactory = KeyFactory.getInstance("RSA")
        val privateKey = keyFactory.generatePrivate(keySpec)
        
        // Generate signature
        val signature = Signature.getInstance("SHA256withRSA")
        signature.initSign(privateKey)
        signature.update(canonicalString.toByteArray())
        val signatureBytes = signature.sign()
        
        // Return Base64 encoded signature
        return Base64.encodeToString(signatureBytes, Base64.NO_WRAP)
    }
}