# MonetaSDK Android Integration Guide

## Installation

Add the MonetaSDK to your app's build.gradle file:

```gradle
dependencies {
    implementation 'com.moneta.sdk:monetasdk:1.0.0'
    // Required for kotlinx.serialization
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.5.1'
    // Required for OkHttp
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
}
```

Add the required permissions to your AndroidManifest.xml:

```xml
<uses-permission android:name="android.permission.INTERNET" />
```

## Initialization

Initialize the MonetaSDK in your Application class:

```kotlin
import android.app.Application
import com.moneta.sdk.MonetaSDK

class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        MonetaSDK.initialize(this, "https://api.moneta.com")
    }
}
```

Make sure to register your Application class in the AndroidManifest.xml:

```xml
<application
    android:name=".MyApplication"
    ...>
```

## Basic Usage

### Onboarding

```kotlin
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import com.moneta.sdk.MonetaSDK
import com.moneta.sdk.util.Result

// Start onboarding process
lifecycleScope.launch {
    when (val result = MonetaSDK.startOnboarding()) {
        is Result.Success -> {
            val onboardingData = result.data
            // Display the user code to the user
            showUserCode(onboardingData.userCode)
        }
        is Result.Error -> {
            // Handle error
            showError("Failed to start onboarding: ${result.exception.message}")
        }
    }
}

// Complete onboarding after user has entered the code on the website
lifecycleScope.launch {
    when (val result = MonetaSDK.completeOnboarding()) {
        is Result.Success -> {
            val userData = result.data
            // Onboarding complete, user can now use the app
            showSuccess("Welcome, ${userData.name}!")
        }
        is Result.Error -> {
            // Handle error
            showError("Failed to complete onboarding: ${result.exception.message}")
        }
    }
}
```

### Transactions

```kotlin
// Get transactions
lifecycleScope.launch {
    when (val result = MonetaSDK.getTransactions(page = 0, size = 10)) {
        is Result.Success -> {
            val transactions = result.data.content
            // Display transactions
            displayTransactions(transactions)
        }
        is Result.Error -> {
            // Handle error
            showError("Failed to get transactions: ${result.exception.message}")
        }
    }
}

// Get a specific transaction
lifecycleScope.launch {
    when (val result = MonetaSDK.getTransaction("transaction-id-123")) {
        is Result.Success -> {
            val transaction = result.data
            // Display transaction details
            displayTransactionDetails(transaction)
        }
        is Result.Error -> {
            // Handle error
            showError("Failed to get transaction: ${result.exception.message}")
        }
    }
}

// Approve a subscription charge
lifecycleScope.launch {
    when (val result = MonetaSDK.approveSubscriptionCharge("transaction-id-123", "approved")) {
        is Result.Success -> {
            val updatedTransaction = result.data
            // Show success message
            showSuccess("Subscription charge approved")
        }
        is Result.Error -> {
            // Handle error
            showError("Failed to approve charge: ${result.exception.message}")
        }
    }
}

// Create a dispute
lifecycleScope.launch {
    when (val result = MonetaSDK.createDispute(
        transactionId = "transaction-id-123",
        reason = "Item not received",
        itemId = "item-id-456"
    )) {
        is Result.Success -> {
            if (result.data) {
                // Show success message
                showSuccess("Dispute created successfully")
            } else {
                // Show error message
                showError("Failed to create dispute")
            }
        }
        is Result.Error -> {
            // Handle error
            showError("Failed to create dispute: ${result.exception.message}")
        }
    }
}
```

### Recommendations

```kotlin
import com.moneta.sdk.model.ContentPreferences
import com.moneta.sdk.model.UserProfile

// Get recommendations
lifecycleScope.launch {
    val contentPreferences = ContentPreferences(interests = listOf("news", "technology", "finance"))
    val userProfile = UserProfile(contentPreferences = contentPreferences)
    
    when (val result = MonetaSDK.getRecommendations(
        userId = "user-id-123",
        userProfile = userProfile,
        offset = 0,
        limit = 10
    )) {
        is Result.Success -> {
            val recommendations = result.data
            // Display recommendations
            displayRecommendations(recommendations)
        }
        is Result.Error -> {
            // Handle error
            showError("Failed to get recommendations: ${result.exception.message}")
        }
    }
}

// Update user profile
lifecycleScope.launch {
    val contentPreferences = ContentPreferences(interests = listOf("sports", "entertainment"))
    
    when (val result = MonetaSDK.updateUserProfile(
        userId = "user-id-123",
        contentPreferences = contentPreferences
    )) {
        is Result.Success -> {
            if (result.data) {
                // Show success message
                showSuccess("User profile updated successfully")
            } else {
                // Show error message
                showError("Failed to update user profile")
            }
        }
        is Result.Error -> {
            // Handle error
            showError("Failed to update user profile: ${result.exception.message}")
        }
    }
}
```

### Publisher Authentication

```kotlin
// Authenticate publisher
lifecycleScope.launch {
    when (val result = MonetaSDK.authPublisher(
        qrCodeId = "qr-code-id-123",
        session = "publisher-session-token"
    )) {
        is Result.Success -> {
            val userData = result.data
            // Authentication successful
            showSuccess("Publisher authenticated: ${userData.name}")
        }
        is Result.Error -> {
            // Handle error
            showError("Failed to authenticate publisher: ${result.exception.message}")
        }
    }
}
```

## Error Handling

The MonetaSDK uses a `Result` class to handle success and error cases. All SDK methods return a `Result` object that can be either `Result.Success` or `Result.Error`.

```kotlin
when (val result = MonetaSDK.startOnboarding()) {
    is Result.Success -> {
        // Handle success case
        val data = result.data
        // Use data...
    }
    is Result.Error -> {
        // Handle error case
        val exception = result.exception
        when (exception) {
            is MonetaException.NetworkException -> {
                // Handle network error
                showError("Network error: ${exception.message}")
            }
            is MonetaException.ApiException -> {
                // Handle API error
                showError("API error (${exception.code}): ${exception.message}")
            }
            is MonetaException.DecodingException -> {
                // Handle decoding error
                showError("Failed to decode response: ${exception.message}")
            }
            is MonetaException.NotInitializedException -> {
                // Handle not initialized error
                showError("SDK not initialized")
            }
            else -> {
                // Handle other errors
                showError("Unknown error: ${exception.message}")
            }
        }
    }
}
```

## Threading

All SDK methods are suspending functions and should be called from a coroutine. The SDK handles threading internally, so you don't need to worry about switching threads.

```kotlin
// Example using lifecycleScope in an Activity or Fragment
lifecycleScope.launch {
    val result = MonetaSDK.startOnboarding()
    // Handle result...
}

// Example using viewModelScope in a ViewModel
viewModelScope.launch {
    val result = MonetaSDK.startOnboarding()
    // Handle result...
}

// Example using a custom coroutine scope
val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
scope.launch {
    val result = MonetaSDK.startOnboarding()
    // Handle result...
}
```

## Troubleshooting

### Common Issues

1. **SDK Not Initialized**: Make sure you call `MonetaSDK.initialize()` before using any SDK methods.

2. **Network Errors**: Check your internet connection and make sure the baseUrl is correct.

3. **API Errors**: Check the error message and code returned by the API. Common issues include:
   - Invalid authentication
   - Missing required parameters
   - Rate limiting

4. **Decoding Errors**: These usually indicate a mismatch between the expected response format and the actual response. Make sure you're using the latest version of the SDK.

### Logging

The SDK uses Android's built-in Log class for logging. You can filter logs with the tag "MonetaSDK" to see SDK-specific logs.

```kotlin
// Example of enabling verbose logging in your application
if (BuildConfig.DEBUG) {
    // Set log level to VERBOSE in your debug builds
}
```

### Contact Support

If you encounter issues that you can't resolve, please contact Moneta <NAME_EMAIL> with the following information:
- SDK version
- Android OS version
- Device model
- Detailed description of the issue
- Steps to reproduce
- Relevant logs
