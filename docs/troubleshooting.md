# Troubleshooting Guide

This guide helps you resolve common issues when integrating and using the Moneta Mobile SDKs.

## Table of Contents

- [Installation Issues](#installation-issues)
- [Initialization Problems](#initialization-problems)
- [Network and API Errors](#network-and-api-errors)
- [Authentication Issues](#authentication-issues)
- [Signature Generation Problems](#signature-generation-problems)
- [Performance Issues](#performance-issues)
- [Platform-Specific Issues](#platform-specific-issues)

## Installation Issues

### iOS: Swift Package Manager Issues

**Problem:** Package resolution fails or takes too long.

**Solutions:**
1. Clear Xcode's derived data: `~/Library/Developer/Xcode/DerivedData`
2. Reset package caches: Xcode → File → Packages → Reset Package Caches
3. Verify the repository URL is correct
4. Check your Xcode version supports the minimum Swift version (5.5+)

**Problem:** Build errors after adding the package.

**Solutions:**
1. Ensure your deployment target is iOS 15.0+ or macOS 12.0+
2. Clean and rebuild your project
3. Check for conflicting dependencies

### Android: Gradle Issues

**Problem:** Dependency resolution conflicts.

**Solutions:**
1. Check for version conflicts with existing dependencies
2. Use `./gradlew dependencies` to analyze the dependency tree
3. Exclude conflicting transitive dependencies:
```gradle
implementation('com.moneta:sdk:1.0.0') {
    exclude group: 'conflicting.group', module: 'conflicting-module'
}
```

**Problem:** Kotlin serialization not working.

**Solutions:**
1. Ensure you have the Kotlin serialization plugin:
```gradle
plugins {
    id 'org.jetbrains.kotlin.plugin.serialization' version '1.7.0'
}
```
2. Add the serialization dependency:
```gradle
implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.5.1'
```

## Initialization Problems

### SDK Not Initialized Error

**Problem:** Getting `MonetaSDKError.notInitialized` or `NotInitializedException`.

**Solutions:**
1. Ensure you call `initialize()` before using any SDK methods
2. For iOS, call in `AppDelegate.didFinishLaunchingWithOptions`
3. For Android, call in `Application.onCreate()`

**Example Fix:**
```swift
// iOS - In AppDelegate
func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    MonetaSDK.shared.initialize(baseUrl: "https://api.moneta.com")
    return true
}
```

```kotlin
// Android - In Application class
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        MonetaSDK.initialize(this, "https://api.moneta.com")
    }
}
```

### Invalid Base URL

**Problem:** Network requests fail immediately.

**Solutions:**
1. Verify the base URL is correct and accessible
2. Ensure the URL uses HTTPS in production
3. Check for trailing slashes (should not have them)
4. Test the URL in a browser or API client

## Network and API Errors

### Connection Timeout

**Problem:** Requests timeout frequently.

**Solutions:**
1. Check your internet connection
2. Verify the server is accessible
3. Increase timeout values if needed:

```swift
// iOS - Custom timeout (if supported in future versions)
MonetaSDK.shared.configure(timeout: 60.0)
```

```kotlin
// Android - Custom timeout (if supported in future versions)
MonetaSDK.configure(timeout = 60000L)
```

### SSL/TLS Errors

**Problem:** SSL handshake failures.

**Solutions:**
1. Ensure you're using HTTPS URLs
2. Check if the server certificate is valid
3. For development, verify test certificates are properly configured
4. On Android, check network security configuration

### API Error Codes

**Common API errors and solutions:**

- **400 Bad Request:** Check request parameters and data format
- **401 Unauthorized:** Verify authentication and signatures
- **403 Forbidden:** Check API permissions and user access
- **404 Not Found:** Verify endpoint URLs and resource IDs
- **429 Too Many Requests:** Implement rate limiting and retry logic
- **500 Internal Server Error:** Server-side issue, contact support

## Authentication Issues

### Onboarding Failures

**Problem:** User onboarding fails or returns invalid codes.

**Solutions:**
1. Verify device name and token are valid
2. Check public key generation and format
3. Ensure secure storage is working properly
4. Verify network connectivity during onboarding

**Debug Steps:**
```swift
// iOS - Debug onboarding
do {
    let verification = try await MonetaSDK.shared.startOnboarding()
    print("User code: \(verification.userCode)")
    print("Device code: \(verification.deviceCode)")
} catch {
    print("Onboarding error: \(error)")
}
```

### Publisher Authentication Issues

**Problem:** QR code authentication fails.

**Solutions:**
1. Verify QR code ID is valid and not expired
2. Check session token format
3. Ensure proper network connectivity
4. Verify the publisher is authorized

## Signature Generation Problems

### Invalid Signatures

**Problem:** Requests fail with signature validation errors.

**Solutions:**
1. Verify private key is stored and retrieved correctly
2. Check onboarding data parsing
3. Ensure canonical string construction is correct
4. Verify RSA-SHA256 signature algorithm

**Debug Steps:**
```swift
// iOS - Check secure storage
let secureStorage = SecureStorage()
do {
    let privateKey = try secureStorage.getData("private_key")
    let onboardingData = try secureStorage.getData("onboarding_data")
    print("Private key exists: \(privateKey != nil)")
    print("Onboarding data exists: \(onboardingData != nil)")
} catch {
    print("Secure storage error: \(error)")
}
```

### Missing Signatures

**Problem:** Requests that should be signed are not being signed.

**Solutions:**
1. Check URL patterns in signature requirement detection
2. Verify signature interceptor is properly configured
3. Ensure endpoints are correctly marked for signing

## Performance Issues

### Slow API Responses

**Problem:** API calls take too long to complete.

**Solutions:**
1. Check network conditions
2. Implement proper pagination for large datasets
3. Use appropriate page sizes (10-50 items)
4. Consider caching frequently accessed data

### Memory Usage

**Problem:** High memory usage or memory leaks.

**Solutions:**
1. Ensure proper disposal of large response objects
2. Use pagination instead of loading all data at once
3. Profile your app to identify memory hotspots
4. Check for retain cycles in callback handlers

### Serialization Performance

**Problem:** JSON parsing is slow.

**Solutions:**
1. Use streaming parsers for large responses
2. Consider background queue processing
3. Implement response caching where appropriate

## Platform-Specific Issues

### iOS Issues

**Keychain Access Errors:**
```swift
// Check keychain accessibility
let secureStorage = SecureStorage()
do {
    try secureStorage.saveData("test_key", value: "test_value")
    let retrieved = try secureStorage.getData("test_key")
    print("Keychain working: \(retrieved == "test_value")")
} catch {
    print("Keychain error: \(error)")
}
```

**Background App Refresh:**
- Ensure network requests complete before app backgrounding
- Handle app lifecycle events properly

### Android Issues

**ProGuard/R8 Issues:**
Add these rules to your `proguard-rules.pro`:
```proguard
# Moneta SDK
-keep class com.moneta.sdk.** { *; }
-keepclassmembers class com.moneta.sdk.** { *; }

# Kotlinx Serialization
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.AnnotationsKt
-keepclassmembers class kotlinx.serialization.json.** {
    *** Companion;
}
```

**Network Security Configuration:**
For development with HTTP endpoints, add to `AndroidManifest.xml`:
```xml
<application
    android:networkSecurityConfig="@xml/network_security_config"
    ... >
```

Create `res/xml/network_security_config.xml`:
```xml
<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">your-dev-server.com</domain>
    </domain-config>
</network-security-config>
```

## Getting Help

If you're still experiencing issues:

1. **Check the logs:** Enable debug logging to see detailed error messages
2. **Review the documentation:** Ensure you're following the integration guide correctly
3. **Search existing issues:** Check the GitHub issues for similar problems
4. **Contact support:** Reach out with detailed error logs and reproduction steps

### Debug Logging

**iOS:**
```swift
// Enable debug logging (if available)
MonetaSDK.shared.setLogLevel(.debug)
```

**Android:**
```kotlin
// Enable debug logging (if available)
MonetaSDK.setLogLevel(LogLevel.DEBUG)
```

### Reporting Issues

When reporting issues, please include:
- Platform and version (iOS 15.0, Android API 21, etc.)
- SDK version
- Complete error messages and stack traces
- Minimal code example that reproduces the issue
- Network conditions and server environment

**Contact Information:**
- 📧 Email: <EMAIL>
- 🐛 GitHub Issues: https://github.com/moneta/moneta-mobile-sdks/issues
- 📖 Documentation: https://docs.moneta.com
