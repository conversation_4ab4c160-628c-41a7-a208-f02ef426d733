# MonetaSDK iOS Integration Guide

## Installation

### CocoaPods

Add the MonetaSDK to your Podfile:

```ruby
# Podfile
target 'YourAppTarget' do
  use_frameworks!
  pod 'MonetaSDK', '~> 1.0.0'
end
```

Then run:

```bash
pod install
```

### Swift Package Manager

1. In Xcode, select File > Add Packages...
2. Enter the package repository URL: `https://github.com/moneta/monetasdk-ios.git`
3. Select the version: `1.0.0` or later
4. Click Add Package

## Initialization

Initialize the MonetaSDK in your AppDelegate or SceneDelegate:

```swift
import MonetaSDK

// In AppDelegate.swift
func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    MonetaSDK.shared.initialize(baseUrl: "https://api.moneta.com")
    return true
}

// Or in SceneDelegate.swift
func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
    MonetaSDK.shared.initialize(baseUrl: "https://api.moneta.com")
}
```

## Basic Usage

### Onboarding

```swift
import MonetaSDK

// Start onboarding process
Task {
    do {
        let onboardingData = try await MonetaSDK.shared.startOnboarding()
        // Display the user code to the user
        showUserCode(onboardingData.userCode)
    } catch {
        // Handle error
        showError("Failed to start onboarding: \(error.localizedDescription)")
    }
}

// Complete onboarding after user has entered the code on the website
Task {
    do {
        let userData = try await MonetaSDK.shared.completeOnboarding()
        // Onboarding complete, user can now use the app
        showSuccess("Welcome, \(userData.name)!")
    } catch {
        // Handle error
        showError("Failed to complete onboarding: \(error.localizedDescription)")
    }
}
```

### Transactions

```swift
// Get transactions
Task {
    do {
        let paginatedResponse = try await MonetaSDK.shared.getTransactions(page: 0, size: 10)
        let transactions = paginatedResponse.content
        // Display transactions
        displayTransactions(transactions)
    } catch {
        // Handle error
        showError("Failed to get transactions: \(error.localizedDescription)")
    }
}

// Get a specific transaction
Task {
    do {
        let transaction = try await MonetaSDK.shared.getTransaction(transactionId: "transaction-id-123")
        // Display transaction details
        displayTransactionDetails(transaction)
    } catch {
        // Handle error
        showError("Failed to get transaction: \(error.localizedDescription)")
    }
}

// Approve a subscription charge
Task {
    do {
        let updatedTransaction = try await MonetaSDK.shared.approveSubscriptionCharge(
            transactionId: "transaction-id-123",
            status: "approved"
        )
        // Show success message
        showSuccess("Subscription charge approved")
    } catch {
        // Handle error
        showError("Failed to approve charge: \(error.localizedDescription)")
    }
}

// Create a dispute
Task {
    do {
        let success = try await MonetaSDK.shared.createDispute(
            transactionId: "transaction-id-123",
            reason: "Item not received",
            itemId: "item-id-456"
        )
        
        if success {
            // Show success message
            showSuccess("Dispute created successfully")
        } else {
            // Show error message
            showError("Failed to create dispute")
        }
    } catch {
        // Handle error
        showError("Failed to create dispute: \(error.localizedDescription)")
    }
}
```

### Recommendations

```swift
import MonetaSDK

// Get recommendations
Task {
    do {
        let contentPreferences = ContentPreferences(interests: ["news", "technology", "finance"])
        let userProfile = UserProfile(contentPreferences: contentPreferences)
        
        let recommendations = try await MonetaSDK.shared.getRecommendations(
            userId: "user-id-123",
            userProfile: userProfile,
            offset: 0,
            limit: 10
        )
        
        // Display recommendations
        displayRecommendations(recommendations)
    } catch {
        // Handle error
        showError("Failed to get recommendations: \(error.localizedDescription)")
    }
}

// Update user profile
Task {
    do {
        let contentPreferences = ContentPreferences(interests: ["sports", "entertainment"])
        
        let success = try await MonetaSDK.shared.updateUserProfile(
            userId: "user-id-123",
            contentPreferences: contentPreferences
        )
        
        if success {
            // Show success message
            showSuccess("User profile updated successfully")
        } else {
            // Show error message
            showError("Failed to update user profile")
        }
    } catch {
        // Handle error
        showError("Failed to update user profile: \(error.localizedDescription)")
    }
}
```

### Publisher Authentication

```swift
// Authenticate publisher
Task {
    do {
        let userData = try await MonetaSDK.shared.authPublisher(
            qrCodeId: "qr-code-id-123",
            session: "publisher-session-token"
        )
        
        // Authentication successful
        showSuccess("Publisher authenticated: \(userData.name)")
    } catch {
        // Handle error
        showError("Failed to authenticate publisher: \(error.localizedDescription)")
    }
}
```

## Error Handling

The MonetaSDK uses Swift's built-in error handling mechanism. All SDK methods can throw errors, which you can catch and handle appropriately.

```swift
do {
    let onboardingData = try await MonetaSDK.shared.startOnboarding()
    // Handle success
} catch let error as MonetaSDKError {
    // Handle MonetaSDK-specific errors
    switch error {
    case .notInitialized:
        showError("SDK not initialized")
    case .networkError(let message):
        showError("Network error: \(message)")
    case .apiError(let code, let message):
        showError("API error (\(code)): \(message)")
    case .decodingError(let message):
        showError("Failed to decode response: \(message)")
    case .cryptoError(let message):
        showError("Cryptography error: \(message)")
    case .secureStorageError(let message):
        showError("Secure storage error: \(message)")
    }
} catch {
    // Handle other errors
    showError("Unknown error: \(error.localizedDescription)")
}
```

## Threading

All SDK methods are async functions and should be called with `await`. The SDK handles threading internally, so you don't need to worry about switching threads.

```swift
// Example using Task in a SwiftUI View
Task {
    do {
        let result = try await MonetaSDK.shared.startOnboarding()
        // Handle result...
    } catch {
        // Handle error...
    }
}

// Example using Task in a UIKit ViewController
Task {
    do {
        let result = try await MonetaSDK.shared.startOnboarding()
        // Handle result...
    } catch {
        // Handle error...
    }
}

// Example with a custom Task
let task = Task {
    do {
        let result = try await MonetaSDK.shared.startOnboarding()
        // Handle result...
    } catch {
        // Handle error...
    }
}

// You can cancel the task if needed
task.cancel()
```

## Troubleshooting

### Common Issues

1. **SDK Not Initialized**: Make sure you call `MonetaSDK.shared.initialize()` before using any SDK methods.

2. **Network Errors**: Check your internet connection and make sure the baseUrl is correct.

3. **API Errors**: Check the error message and code returned by the API. Common issues include:
   - Invalid authentication
   - Missing required parameters
   - Rate limiting

4. **Decoding Errors**: These usually indicate a mismatch between the expected response format and the actual response. Make sure you're using the latest version of the SDK.

### Logging

The SDK uses Swift's built-in print or OSLog for logging. You can filter logs with the subsystem "com.moneta.sdk" to see SDK-specific logs.

```swift
// Example of enabling verbose logging in your application
if DEBUG {
    // Set log level to VERBOSE in your debug builds
}
```

### Contact Support

If you encounter issues that you can't resolve, please contact Moneta <NAME_EMAIL> with the following information:
- SDK version
- iOS version
- Device model
- Detailed description of the issue
- Steps to reproduce
- Relevant logs