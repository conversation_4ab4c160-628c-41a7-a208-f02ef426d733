# Moneta SDK API Reference

This document provides a comprehensive reference for all APIs available in the Moneta Mobile SDKs.

## Table of Contents

- [Initialization](#initialization)
- [MembershipOrgClient](#membershiporgclient)
- [MonetaCoreClient](#monetacoreclient)
- [UAClient](#uaclient)
- [Error Handling](#error-handling)
- [Models](#models)

## Initialization

### iOS

```swift
import MonetaSDK

// Initialize the SDK
MonetaSDK.shared.initialize(baseUrl: "https://api.moneta.com")
```

### Android

```kotlin
import com.moneta.sdk.MonetaSDK

// Initialize the SDK
MonetaSDK.initialize(context, "https://api.moneta.com")
```

## MembershipOrgClient

The MembershipOrgClient handles user onboarding, transactions, feeds, policies, and settlements.

### Onboarding

#### `fetchMoInfo()`
Get membership organization information.

**iOS:**
```swift
let info = try await MonetaSDK.shared.membershipOrgClient.fetchMoInfo()
```

**Android:**
```kotlin
val result = MonetaSDK.instance.membershipOrgClient.fetchMoInfo()
```

#### `verifyOnboarding(requestId:request:)`
Verify user onboarding with device code.

**Parameters:**
- `requestId: String` - The onboarding request ID
- `request: OnboardingRequest` - The onboarding request data

**iOS:**
```swift
let request = OnboardingRequest(
    deviceName: "iPhone 15",
    deviceToken: "device_token_123",
    publicKey: "public_key_base64"
)
let response = try await MonetaSDK.shared.membershipOrgClient.verifyOnboarding(
    requestId: "req_123",
    request: request
)
```

#### `checkUserCodeIsVerified(requestId:deviceCode:)`
Check if user code is verified.

**Parameters:**
- `requestId: String` - The onboarding request ID
- `deviceCode: String` - The device code

### Transactions

#### `fetchTransactions(month:pageSize:pageBefore:pageAfter:)`
Get user transactions with optional filtering and pagination.

**Parameters:**
- `month: String?` - Filter by month (YYYY-MM format)
- `pageSize: Int?` - Number of items per page
- `pageBefore: String?` - Cursor for previous page
- `pageAfter: String?` - Cursor for next page

**iOS:**
```swift
let transactions = try await MonetaSDK.shared.membershipOrgClient.fetchTransactions(
    month: "2024-01",
    pageSize: 20,
    pageAfter: "cursor_123"
)
```

#### `getTransaction(id:)`
Get a specific transaction by ID.

**Parameters:**
- `id: String` - The transaction ID

#### `getRelatedTransactions(id:pageSize:pageOrder:)`
Get transactions related to a specific transaction.

**Parameters:**
- `id: String` - The transaction ID
- `pageSize: Int?` - Number of items per page
- `pageOrder: String?` - Sort order

### User Feeds

#### `fetchUserFeeds(pageSize:pageAfter:)`
Get user notification feeds.

**Parameters:**
- `pageSize: Int?` - Number of items per page
- `pageAfter: String?` - Cursor for next page

#### `getFeedById(feedId:)`
Get a specific feed by ID.

**Parameters:**
- `feedId: String` - The feed ID

#### `markFeedsAsRead(body:)`
Mark feeds as read.

**Parameters:**
- `body: [String: [String]]` - Feed IDs to mark as read

#### `deleteFeeds(body:)`
Delete feeds.

**Parameters:**
- `body: [String: [String]]` - Feed IDs to delete

### Policies

#### `fetchPolicies()`
Get user policy settings.

#### `setPolicy(type:body:)`
Set a user policy.

**Parameters:**
- `type: String` - The policy type
- `body: [String: Any]` - The policy data

#### `fetchIndustries()`
Get available industries for policy configuration.

#### `fetchPublishers()`
Get available publishers for policy configuration.

### Settlement & Balance

#### `fetchBalance()`
Get user account balance.

**iOS:**
```swift
let balance = try await MonetaSDK.shared.membershipOrgClient.fetchBalance()
print("Balance: \(balance.balance) \(balance.currency)")
```

#### `fetchLatestSettlementBillingCycle()`
Get the latest settlement billing cycle.

#### `fetchSettlementBillingCycle(billingFrom:billingTo:)`
Get settlement billing cycles within a date range.

**Parameters:**
- `billingFrom: String?` - Start date (YYYY-MM-DD)
- `billingTo: String?` - End date (YYYY-MM-DD)

#### `fetchSettlementBillingCycleConsumption(code:)`
Get consumption data for a billing cycle.

**Parameters:**
- `code: String` - The billing cycle code

### Disputes

#### `createDisputeRequest(request:)`
Create a new dispute request.

**Parameters:**
- `request: CreateDisputeRequest` - The dispute request data

**iOS:**
```swift
let request = CreateDisputeRequest(
    transactionId: "txn_123",
    reason: "Unauthorized charge",
    itemId: "item_456"
)
let dispute = try await MonetaSDK.shared.membershipOrgClient.createDisputeRequest(request: request)
```

#### `fetchDisputes(transactionId:)`
Get dispute requests, optionally filtered by transaction.

**Parameters:**
- `transactionId: String?` - Filter by transaction ID

## MonetaCoreClient

The MonetaCoreClient handles publisher authentication and article management.

### Publisher Authentication

#### `authPublisher(qrCodeId:request:)`
Authenticate a publisher using QR code.

**Parameters:**
- `qrCodeId: String` - The QR code ID
- `request: PublisherLoginRequest` - The login request data

**iOS:**
```swift
let request = PublisherLoginRequest(session: "session_token_123")
let response = try await MonetaSDK.shared.monetaCoreClient.authPublisher(
    qrCodeId: "qr_123",
    request: request
)
```

#### `checkPublisherLoginStatus(qrCodeId:)`
Check the status of a publisher login.

**Parameters:**
- `qrCodeId: String` - The QR code ID

### Articles

#### `fetchArticles(categories:before:)`
Get articles with optional filtering.

**Parameters:**
- `categories: String?` - Comma-separated category names
- `before: String?` - Cursor for pagination

#### `fetchArticleCategories()`
Get available article categories.

## UAClient

The UAClient handles user analytics and recommendations.

### Recommendations

#### `fetchRecommendations(request:)`
Get personalized content recommendations.

**Parameters:**
- `request: RecommendationsRequest` - The recommendation request data

#### `fetchRssContent(userId:limit:offset:)`
Get RSS content for a user (deprecated).

**Parameters:**
- `userId: String` - The user ID
- `limit: Int?` - Number of items to return
- `offset: Int?` - Offset for pagination

### User Profile

#### `fetchUserProfile(userId:)`
Get detailed user profile.

**Parameters:**
- `userId: String` - The user ID

#### `updateUserProfile(request:)`
Update user profile preferences.

**Parameters:**
- `request: UpdateUAUserProfileRequest` - The profile update data

**iOS:**
```swift
let contentPreferences = UAUserContentPreferences(interests: ["tech", "finance"])
let request = UpdateUAUserProfileRequest(
    userId: "user_123",
    contentPreferences: contentPreferences
)
let response = try await MonetaSDK.shared.uaClient.updateUserProfile(request: request)
```

### Interests

#### `fetchInterests()`
Get available interest categories.

## Error Handling

### iOS Error Types

```swift
public enum MonetaSDKError: Error {
    case networkError(String)
    case apiError(Int, String)
    case decodingError(String)
    case notInitialized
    case secureStorageError(String)
    case cryptoError(String)
}
```

### Android Error Types

```kotlin
sealed class MonetaException(message: String, cause: Throwable? = null) : Exception(message, cause) {
    class NotInitializedException : MonetaException("MonetaSDK is not initialized")
    class NetworkException(message: String, cause: Throwable? = null) : MonetaException("Network error: $message", cause)
    class ApiException(val code: Int, message: String) : MonetaException("API error ($code): $message")
    class DecodingException(message: String, cause: Throwable? = null) : MonetaException("Failed to decode response: $message", cause)
    class CryptoException(message: String, cause: Throwable? = null) : MonetaException("Cryptography error: $message", cause)
    class SecureStorageException(message: String, cause: Throwable? = null) : MonetaException("Secure storage error: $message", cause)
}
```

## Models

### Request Models

#### `OnboardingRequest`
```swift
public struct OnboardingRequest: Codable {
    public let deviceName: String
    public let deviceToken: String
    public let publicKey: String
}
```

#### `CreateDisputeRequest`
```swift
public struct CreateDisputeRequest: Codable {
    public let transactionId: String
    public let reason: String
    public let itemId: String?
}
```

#### `RecommendationsRequest`
```swift
public struct RecommendationsRequest: Codable {
    public let userId: String
    public let userProfile: UserProfile
    public let offset: Int
    public let limit: Int
}
```

### Response Models

#### `TransactionResponse`
```swift
public struct TransactionResponse: Codable {
    public let id: String
    public let type: String
    public let userId: String
    public let publisherId: String
    public let publisherName: String
    public let amount: Double
    public let interchangeFee: Double
    public let status: String
    public let disputeStatus: String?
    public let currencyCode: String
    public let description: String
    public let createdAt: String
    public let updatedAt: String
    public let finalizedAt: String?
}
```

#### `UserBalanceResponse`
```swift
public struct UserBalanceResponse: Codable {
    public let balance: Double
    public let currency: String
    public let lastUpdated: String
    public let pendingAmount: Double?
}
```

#### `UAUserProfileResponse`
```swift
public struct UAUserProfileResponse: Codable {
    public let birthDate: String
    public let metadata: UAUserMetadata
    public let createdAt: String
    public let deviceIds: [String]
    public let email: String
    public let language: String
    public let updatedAt: String
    public let userId: String
    public let contentPreferences: UAUserContentPreferences
    public let qualityMetrics: UAUserQualityMetrics
    public let communicationPreferences: UAUserCommunicationPreferences
    public let demographics: UAUserDemographics
    public let id: String
    public let phone: String
    public let monetaUserId: String?
}
```

For complete model definitions, see the source code in:
- iOS: `Sources/MonetaSDK/Models/`
- Android: `src/main/kotlin/com/moneta/sdk/model/`
