# MonetaSDK

MonetaSDK is a native mobile Software Development Kit (SDK) designed to provide a robust, secure, and easy-to-integrate interface for consuming the Moneta platform's core functionalities. This SDK abstracts the underlying network communication, signature generation, and data parsing, allowing third-party applications to seamlessly interact with Moneta APIs.

## Features

### ✅ Complete API Coverage
- **47 endpoints** across 3 client types (MembershipOrg, MonetaCore, UA)
- **25+ response models** with full JSON serialization support
- **Comprehensive request models** for all API interactions

### ✅ Security & Authentication
- **RSA key pair generation** for secure device registration
- **Request signing** with RSA-SHA256 signatures
- **Secure storage** using platform-specific secure storage (Keychain/Keystore)
- **Automatic signature injection** for authenticated endpoints

### ✅ Robust Error Handling
- **Typed error responses** with detailed error information
- **Network error handling** with retry capabilities
- **API error mapping** with status codes and messages
- **Graceful degradation** for offline scenarios

### ✅ Developer Experience
- **Comprehensive unit tests** for all models and core functionality
- **Integration tests** with mock server responses
- **Performance tests** for serialization and network operations
- **Detailed documentation** with code examples

## Platforms

- Android (Kotlin)
- iOS (Swift)

## Installation

### Android

Add the MonetaSDK to your app's build.gradle file:

```gradle
dependencies {
    implementation 'com.moneta.sdk:monetasdk:1.0.0'
    // Required for kotlinx.serialization
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.5.1'
    // Required for OkHttp
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
}
```

### iOS

#### CocoaPods

Add the MonetaSDK to your Podfile:

```ruby
# Podfile
target 'YourAppTarget' do
  use_frameworks!
  pod 'MonetaSDK', '~> 1.0.0'
end
```

Then run:

```bash
pod install
```

#### Swift Package Manager

1. In Xcode, select File > Add Packages...
2. Enter the package repository URL: `https://github.com/moneta/monetasdk-ios.git`
3. Select the version: `1.0.0` or later
4. Click Add Package

## Quick Start

### Android

Initialize the SDK in your Application class:

```kotlin
import android.app.Application
import com.moneta.sdk.MonetaSDK

class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        MonetaSDK.initialize(this, "https://api.moneta.com")
    }
}
```

### iOS

Initialize the SDK in your AppDelegate or SceneDelegate:

```swift
import MonetaSDK

// In AppDelegate.swift
func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    MonetaSDK.shared.initialize(baseUrl: "https://api.moneta.com")
    return true
}
```

## Core API Clients

### MembershipOrgClient
Handles user onboarding, transactions, feeds, policies, and settlements:

```swift
// iOS Example
let transactions = try await sdk.membershipOrgClient.fetchTransactions(
    month: "2024-01",
    pageSize: 20
)

let balance = try await sdk.membershipOrgClient.fetchBalance()
```

```kotlin
// Android Example
val transactionsResult = MonetaSDK.instance.membershipOrgClient.fetchTransactions(
    month = "2024-01",
    pageSize = 20
)

val balanceResult = MonetaSDK.instance.membershipOrgClient.fetchBalance()
```

**Key endpoints:**
- `fetchMoInfo()` - Get membership organization information
- `verifyOnboarding()` - Verify user onboarding with device code
- `fetchTransactions()` - Get user transactions with pagination
- `fetchUserFeeds()` - Get user notification feeds
- `fetchPolicies()` - Get user policy settings
- `fetchBalance()` - Get user account balance
- And 19+ more endpoints...

### MonetaCoreClient
Handles publisher authentication and article management:

```swift
// iOS Example
let loginStatus = try await sdk.monetaCoreClient.checkPublisherLoginStatus(
    qrCodeId: "qr_123"
)

let articles = try await sdk.monetaCoreClient.fetchArticles(
    categories: "tech,finance"
)
```

**Key endpoints:**
- `authPublisher()` - Authenticate publisher via QR code
- `checkPublisherLoginStatus()` - Check publisher login status
- `fetchArticles()` - Get articles with category filtering
- `fetchArticleCategories()` - Get available article categories

### UAClient
Handles user analytics and recommendations:

```swift
// iOS Example
let recommendations = try await sdk.uaClient.fetchRecommendations(request)
let userProfile = try await sdk.uaClient.fetchUserProfile(userId: "user_123")
```

**Key endpoints:**
- `fetchRecommendations()` - Get personalized content recommendations
- `fetchUserProfile()` - Get detailed user profile
- `updateUserProfile()` - Update user preferences
- `fetchInterests()` - Get available interest categories

## Advanced Usage

### Error Handling

```swift
// iOS
do {
    let transactions = try await MonetaSDK.shared.getTransactions(page: 1, size: 10)
    // Handle success
} catch MonetaSDKError.networkError(let message) {
    print("Network error: \(message)")
} catch MonetaSDKError.apiError(let code, let message) {
    print("API error \(code): \(message)")
} catch {
    print("Unknown error: \(error)")
}
```

```kotlin
// Android
when (val result = MonetaSDK.instance.getTransactions(page = 1, size = 10)) {
    is Result.Success -> {
        // Handle success
        val transactions = result.data
    }
    is Result.Error -> {
        when (val exception = result.exception) {
            is MonetaException.NetworkException -> {
                println("Network error: ${exception.message}")
            }
            is MonetaException.ApiException -> {
                println("API error ${exception.code}: ${exception.message}")
            }
            else -> {
                println("Unknown error: ${exception.message}")
            }
        }
    }
}
```

### Custom Configuration

```swift
// iOS - Custom configuration
MonetaSDK.shared.initialize(
    baseUrl: "https://api.moneta.com",
    timeout: 30.0,
    enableLogging: true
)
```

```kotlin
// Android - Custom configuration
MonetaSDK.initialize(
    context = this,
    baseUrl = "https://api.moneta.com",
    timeout = 30000L,
    enableLogging = true
)
```

## Testing

### iOS
```bash
cd ios
swift test
```

### Android
```bash
cd android
./gradlew test
```

## Requirements

### iOS
- iOS 15.0+ / macOS 12.0+
- Xcode 13.0+
- Swift 5.5+

### Android
- Android API level 21+
- Kotlin 1.7+
- Gradle 7.0+

## Documentation

For detailed documentation and examples, see:

- [iOS Integration Guide](ios/README.md)
- [Android Integration Guide](android/README.md)
- [API Reference](docs/api-reference.md)
- [Migration Guide](docs/migration-guide.md)
- [Troubleshooting](docs/troubleshooting.md)

## Support

For support and questions:
- 📧 Email: <EMAIL>
- 📖 Documentation: https://docs.moneta.com
- 🐛 Issues: https://github.com/moneta/moneta-mobile-sdks/issues

## License

Copyright © 2025 Moneta, Inc. All rights reserved.