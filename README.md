# MonetaSDK

MonetaSDK is a native mobile Software Development Kit (SDK) designed to provide a robust, secure, and easy-to-integrate interface for consuming the Moneta platform's core functionalities. This SDK abstracts the underlying network communication, signature generation, and data parsing, allowing third-party applications to seamlessly interact with Moneta APIs.

## Features

- Simplified API integration with Moneta services
- Secure communication with signature generation and header management
- Consistent API and behavior across Android and iOS platforms
- Comprehensive documentation and examples
- Modular design for future enhancements

## Platforms

- Android (Kotlin)
- iOS (Swift)

## Installation

### Android

Add the MonetaSDK to your app's build.gradle file:

```gradle
dependencies {
    implementation 'com.moneta.sdk:monetasdk:1.0.0'
    // Required for kotlinx.serialization
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.5.1'
    // Required for OkHttp
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
}
```

### iOS

#### CocoaPods

Add the MonetaSDK to your Podfile:

```ruby
# Podfile
target 'YourAppTarget' do
  use_frameworks!
  pod 'MonetaSDK', '~> 1.0.0'
end
```

Then run:

```bash
pod install
```

#### Swift Package Manager

1. In Xcode, select File > Add Packages...
2. Enter the package repository URL: `https://github.com/moneta/monetasdk-ios.git`
3. Select the version: `1.0.0` or later
4. Click Add Package

## Quick Start

### Android

Initialize the SDK in your Application class:

```kotlin
import android.app.Application
import com.moneta.sdk.MonetaSDK

class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        MonetaSDK.initialize(this, "https://api.moneta.com")
    }
}
```

### iOS

Initialize the SDK in your AppDelegate or SceneDelegate:

```swift
import MonetaSDK

// In AppDelegate.swift
func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    MonetaSDK.shared.initialize(baseUrl: "https://api.moneta.com")
    return true
}
```

## Documentation

For detailed documentation and examples, see:

- [Android Integration Guide](docs/android-guide.md)
- [iOS Integration Guide](docs/ios-guide.md)

## License

Copyright © 2025 Moneta, Inc. All rights reserved.